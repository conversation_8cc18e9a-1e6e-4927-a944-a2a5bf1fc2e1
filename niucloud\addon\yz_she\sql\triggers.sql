-- ===================================================================
-- 奢侈品回收插件 - 触发器安装文件
-- 说明：此文件包含插件所需的触发器，需要在主表创建完成后手动执行
-- 注意：执行前请将 {{prefix}} 替换为实际的表前缀
-- ===================================================================

-- 设置SQL模式，确保兼容性
SET sql_mode = '';

-- ----------------------------
-- 触发器1：估价订单自动取消时间设置
-- 功能：当估价订单状态从"待确认"变为"待发货"时，自动设置48小时后的取消时间
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_quote_orders_auto_cancel_time`;

DELIMITER ;;
CREATE TRIGGER `tr_quote_orders_auto_cancel_time` 
BEFORE UPDATE ON `{{prefix}}yz_she_quote_orders` 
FOR EACH ROW 
BEGIN
  -- 当状态从待确认(2)变为待发货(3)时，设置48小时后自动取消时间
  IF OLD.status = 2 AND NEW.status = 3 THEN
    SET NEW.auto_cancel_time = DATE_ADD(NOW(), INTERVAL 48 HOUR);
  END IF;

  -- 当状态变为已完成(4)或已取消(5)时，清除自动取消时间
  IF NEW.status IN (4, 5) THEN
    SET NEW.auto_cancel_time = NULL;
  END IF;
END;;
DELIMITER ;

-- ----------------------------
-- 触发器2：回收订单状态变更日志记录
-- 功能：当回收订单状态发生变化时，自动在日志表中记录变更信息
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_recycle_orders_status_log`;

DELIMITER ;;
CREATE TRIGGER `tr_recycle_orders_status_log` 
AFTER UPDATE ON `{{prefix}}yz_she_recycle_orders` 
FOR EACH ROW 
BEGIN
  -- 当状态发生变化时，自动记录日志
  IF OLD.status != NEW.status THEN
    INSERT INTO `{{prefix}}yz_she_recycle_order_logs` (
      `recycle_order_id`,
      `from_status`,
      `to_status`,
      `operator_type`,
      `change_reason`,
      `create_time`
    ) VALUES (
      NEW.id,
      OLD.status,
      NEW.status,
      3, -- 系统操作
      CONCAT('状态从 ', OLD.status, ' 变更为 ', NEW.status),
      UNIX_TIMESTAMP()
    );
  END IF;
END;;
DELIMITER ;

-- ----------------------------
-- 验证触发器是否创建成功
-- ----------------------------
-- 可以执行以下查询来验证触发器是否创建成功：
-- SHOW TRIGGERS LIKE '%yz_she%';

-- ----------------------------
-- 触发器使用说明
-- ----------------------------
/*
1. 触发器1 (tr_quote_orders_auto_cancel_time)：
   - 当估价订单状态从2(待确认)变为3(待发货)时，自动设置auto_cancel_time为48小时后
   - 当订单状态变为4(已完成)或5(已取消)时，清除auto_cancel_time

2. 触发器2 (tr_recycle_orders_status_log)：
   - 当回收订单状态发生任何变化时，自动在yz_she_recycle_order_logs表中记录
   - 记录包括：订单ID、原状态、新状态、操作类型(3=系统)、变更原因、创建时间

3. 安装步骤：
   a) 确保所有相关表已经创建完成
   b) 将此文件中的{{prefix}}替换为实际的表前缀
   c) 在MySQL中执行修改后的SQL语句
   d) 使用SHOW TRIGGERS命令验证创建结果

4. 如果遇到权限问题，请确保数据库用户具有CREATE TRIGGER权限
*/
