{"basicSettings": "同城配送基础设置", "timeIsOpen": "配送时间设置", "timeIsOpenTips": "开启后，买家下单选择同城配送时，可选择配送时间，提交订单后，将在买家备注中显示。", "close": "关闭", "open": "开启", "everyDay": "每天", "monday": "周一", "tuesday": "周二", "wednesday": "周三", "thursday": "周四", "friday": "周五", "saturday": "周六", "sunday": "周日", "timeWeekRequire": "请选择配送时间", "deliveryTimeSetting": "配送时间设置", "feeType": "收费标准", "region": "按区域收取配送费", "distance": "按距离收取配送费", "district": "按行政区域收取配送费", "feeSetting": "费用设置", "weightFee": "续重收费", "feeSettingTextOne": "km内按", "feeSettingTextTwo": "元收取配送费，每超出", "feeSettingTextThree": "km费用增加", "priceUnit": "元", "weightFeeTextOne": "商品重量", "weightFeeTextTwo": "kg 内不额外收费，每超出", "weightFeeTextThree": "kg 费用增加", "areaName": "区域名称", "startPrice": "起送价", "deliveryPrice": "配送费", "areaType": "划分方式", "radius": "半径", "custom": "自定义", "addDeliveryArea": "添加配送区域", "baseDistRequire": "请输入起始公里数", "gradDistRequire": "请输入超出公里数", "basePriceRequire": "请输入起始公里内的配送费用", "gradPriceRequire": "请输入每超出公里部分的费用", "areaNameRequire": "请输入区域名称", "startPriceRequire": "请输入起送价", "startPriceMin": "起送价不能小于0", "deliveryPriceRequire": "请输入配送费", "deliveryPriceMin": "配送费不能小于0", "areaPlaceholder": "请添加配送区域", "deliveryType": "配送方式", "business": "商家自配送", "deliveryTypeRequire": "至少需选择一种配送方式", "deliveryAddress": "取货地址", "defaultDeliveryAddressEmpty": "请先配置默认发货地址", "toSetting": "去配置", "update": "修改", "deliveryAddressChange": "取货地址已变更请注意是否需重新调整配送区域"}