<?php
return [
    [
        'menu_name' => '商城系统',
        'menu_key' => 'shop',
        'menu_short_name' => '商城',
        'parent_key' => '',
        'menu_type' => '0',
        'icon' => 'iconfont iconshangchengshequ',
        'api_url' => '',
        'router_path' => '',
        'view_path' => '',
        'methods' => '',
        'sort' => '110',
        'status' => '1',
        'is_show' => '1',
        'children' => [
            [
                'menu_name' => '概况',
                'menu_key' => 'shop_index',
                'menu_short_name' => '概况',
                'menu_type' => '1',
                'icon' => 'iconfont icongaikuang1',
                'api_url' => 'shop/index',
                'router_path' => 'shop/index',
                'view_path' => 'index/index',
                'methods' => 'get',
                'sort' => '100',
                'status' => '1',
                'is_show' => '1',
            ],
            [
                'menu_name' => '商品管理',
                'menu_key' => 'shop_goods',
                'menu_short_name' => '商品',
                'menu_type' => '0',
                'icon' => 'iconfont iconshangpinguanli1',
                'api_url' => '',
                'router_path' => '',
                'view_path' => '',
                'methods' => 'get',
                'sort' => '90',
                'status' => '1',
                'is_show' => '1',
                'children' => [
                    [
                        'menu_name' => '商品列表',
                        'menu_key' => 'shop_goods_list',
                        'menu_short_name' => '商品列表',
                        'menu_type' => '1',
                        'icon' => 'iconfont iconshangpinliebiao1',
                        'api_url' => 'shop/goods',
                        'router_path' => 'shop/goods/list',
                        'view_path' => 'goods/list',
                        'methods' => 'get',
                        'sort' => '90',
                        'status' => '1',
                        'is_show' => '1',
                    ],
                    [
                        'menu_name' => '商品分类',
                        'menu_key' => 'shop_goods_category_list',
                        'menu_short_name' => '商品分类',
                        'menu_type' => '1',
                        'icon' => 'iconfont iconshangpinfenlei',
                        'api_url' => 'shop/goods/category',
                        'router_path' => 'shop/goods/category',
                        'view_path' => 'goods/category',
                        'methods' => 'get',
                        'sort' => '89',
                        'status' => '1',
                        'is_show' => '1',
                        'children' => [
                            [
                                'menu_name' => '商品分类添加',
                                'menu_key' => 'shop_goods_category_add',
                                'menu_short_name' => '商品分类添加',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/category',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'post',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '商品分类编辑',
                                'menu_key' => 'shop_goods_category_edit',
                                'menu_short_name' => '商品分类编辑',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/category/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '商品分类删除',
                                'menu_key' => 'shop_goods_category_delete',
                                'menu_short_name' => '商品分类删除',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/category/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'delete',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '商品分类拖拽排序',
                                'menu_key' => 'shop_goods_category_sort',
                                'menu_short_name' => '商品分类拖拽排序',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/category/update',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'post',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '分类设置',
                        'menu_key' => 'shop_goods_category_config',
                        'menu_short_name' => '分类设置',
                        'menu_type' => '1',
                        'icon' => 'iconfont iconfenleishezhi',
                        'api_url' => '',
                        'router_path' => 'shop/goods/category/config',
                        'view_path' => 'goods/category_config',
                        'methods' => '',
                        'sort' => '88',
                        'status' => '1',
                        'is_show' => '0',
                        'children' => [
                            [
                                'menu_name' => '分类设置编辑',
                                'menu_key' => 'shop_goods_category_config_set',
                                'menu_short_name' => '分类设置编辑',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/category/config',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'post',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '商品标签分组',
                        'menu_key' => 'shop_goods_label_group_list',
                        'menu_short_name' => '商品标签分组',
                        'menu_type' => '1',
                        'icon' => 'iconfont iconshangpinbiaoqian',
                        'api_url' => 'shop/goods/label/group',
                        'router_path' => 'shop/goods/label/group',
                        'view_path' => 'goods/label_group_list',
                        'methods' => 'get',
                        'sort' => '88',
                        'status' => '1',
                        'is_show' => '0',
                        'children' => [
                            [
                                'menu_name' => '商品标签分组添加',
                                'menu_key' => 'shop_goods_label_group_add',
                                'menu_short_name' => '商品标签分组添加',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/label/group',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'post',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '商品标签分组编辑',
                                'menu_key' => 'shop_goods_label_group_edit',
                                'menu_short_name' => '商品标签分组编辑',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/label/group/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '商品标签分组删除',
                                'menu_key' => 'shop_goods_label_group_delete',
                                'menu_short_name' => '商品标签分组删除',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/label/group/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'delete',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '商品标签',
                        'menu_key' => 'shop_goods_label_list',
                        'menu_short_name' => '商品标签',
                        'menu_type' => '1',
                        'icon' => 'iconfont iconshangpinbiaoqian',
                        'api_url' => 'shop/goods/label',
                        'router_path' => 'shop/goods/label',
                        'view_path' => 'goods/label_list',
                        'methods' => 'get',
                        'sort' => '87',
                        'status' => '1',
                        'is_show' => '1',
                        'children' => [
                            [
                                'menu_name' => '商品标签添加',
                                'menu_key' => 'shop_goods_label_add',
                                'menu_short_name' => '商品标签添加',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/label',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'post',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '商品标签编辑',
                                'menu_key' => 'shop_goods_label_edit',
                                'menu_short_name' => '商品标签编辑',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/label/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '商品标签删除',
                                'menu_key' => 'shop_goods_label_delete',
                                'menu_short_name' => '商品标签删除',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/label/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'delete',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '商品标签复制',
                                'menu_key' => 'shop_goods_label_copy',
                                'menu_short_name' => '商品标签复制',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/label/copy/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'post',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '商品品牌',
                        'menu_key' => 'shop_goods_brand_list',
                        'menu_short_name' => '商品品牌',
                        'menu_type' => '1',
                        'icon' => 'iconfont iconshangpinpinpai',
                        'api_url' => 'shop/goods/brand',
                        'router_path' => 'shop/goods/brand',
                        'view_path' => 'goods/brand_list',
                        'methods' => 'get',
                        'sort' => '86',
                        'status' => '1',
                        'is_show' => '1',
                        'children' => [
                            [
                                'menu_name' => '商品品牌添加',
                                'menu_key' => 'shop_goods_brand_add',
                                'menu_short_name' => '商品品牌添加',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/brand',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'post',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '商品品牌编辑',
                                'menu_key' => 'shop_goods_brand_edit',
                                'menu_short_name' => '商品品牌编辑',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/brand/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '商品品牌删除',
                                'menu_key' => 'shop_goods_brand_delete',
                                'menu_short_name' => '商品品牌删除',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/brand/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'delete',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '商品服务',
                        'menu_key' => 'shop_goods_service_list',
                        'menu_short_name' => '商品服务',
                        'menu_type' => '1',
                        'icon' => 'iconfont iconshangpinfuwu',
                        'api_url' => 'shop/goods/service',
                        'router_path' => 'shop/goods/service',
                        'view_path' => 'goods/service',
                        'methods' => 'get',
                        'sort' => '85',
                        'status' => '1',
                        'is_show' => '1',
                        'children' => [
                            [
                                'menu_name' => '商品服务添加',
                                'menu_key' => 'shop_goods_service_add',
                                'menu_short_name' => '商品服务添加',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/service',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'post',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '商品服务编辑',
                                'menu_key' => 'shop_goods_service_edit',
                                'menu_short_name' => '商品服务编辑',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/service/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '商品服务删除',
                                'menu_key' => 'shop_goods_service_delete',
                                'menu_short_name' => '商品服务删除',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/service/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'delete',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '参数模板',
                        'menu_key' => 'shop_goods_attr_list',
                        'menu_short_name' => '参数模板',
                        'menu_type' => '1',
                        'icon' => 'iconfont iconshangpinfuwu',
                        'api_url' => 'shop/goods/attr',
                        'router_path' => 'shop/goods/attr',
                        'view_path' => 'goods/attr',
                        'methods' => 'get',
                        'sort' => '84',
                        'status' => '1',
                        'is_show' => '1',
                        'children' => [
                            [
                                'menu_name' => '修改参数模板排序号',
                                'menu_key' => 'shop_goods_attr_sort_edit',
                                'menu_short_name' => '修改参数模板排序号',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/attr/sort',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '修改参数模板名称',
                                'menu_key' => 'shop_goods_attr_name_edit',
                                'menu_short_name' => '修改参数模板名称',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/attr/attr_name',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '修改参数模板值',
                                'menu_key' => 'shop_goods_attr_value_edit',
                                'menu_short_name' => '修改参数模板值',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/attr/attr_value',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '商品统计',
                        'menu_key' => 'shop_stat_goods',
                        'menu_short_name' => '商品统计',
                        'menu_type' => '1',
                        'icon' => 'iconfont icona-dingdanliebiao',
                        'api_url' => 'stat/goods',
                        'router_path' => 'shop/stat/goods',
                        'view_path' => 'stat/goods',
                        'methods' => 'get',
                        'sort' => '83',
                        'status' => '1',
                        'is_show' => '1',
                    ],
                    [
                        'menu_name' => '商品设置',
                        'menu_key' => 'shop_goods_search_config',
                        'menu_short_name' => '搜索设置',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'shop/goods/config/search',
                        'router_path' => 'shop/goods/search_config',
                        'view_path' => 'goods/search_config',
                        'methods' => 'post',
                        'sort' => '83',
                        'status' => '1',
                        'is_show' => '1',
                    ],
                    [
                        'menu_name' => '编码唯一性设置',
                        'menu_key' => 'shop_goods_unique_config',
                        'menu_short_name' => '编码唯一性设置',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'shop/goods/config/unique',
                        'router_path' => 'shop/goods/unique_config',
                        'view_path' => 'goods/unique_config',
                        'methods' => 'post',
                        'sort' => '83',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '商品排序设置',
                        'menu_key' => 'shop_goods_sort_config',
                        'menu_short_name' => '排序设置',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'shop/goods/config/sort',
                        'router_path' => 'shop/goods/sort_config',
                        'view_path' => 'goods/sort_config',
                        'methods' => 'post',
                        'sort' => '83',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '回收站',
                        'menu_key' => 'shop_goods_recycle_list',
                        'menu_short_name' => '回收站',
                        'menu_type' => '1',
                        'icon' => 'iconfont iconhuishouzhan',
                        'api_url' => 'shop/goods/recycle',
                        'router_path' => 'shop/goods/recycle',
                        'view_path' => 'goods/recycle',
                        'methods' => 'get',
                        'sort' => '82',
                        'status' => '1',
                        'is_show' => '1',
                        'children' => [
                            [
                                'menu_name' => '商品恢复',
                                'menu_key' => 'shop_goods_recycle',
                                'menu_short_name' => '商品恢复',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/recycle',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '商品添加',
                        'menu_key' => 'shop_goods_virtual_add',
                        'menu_short_name' => '商品添加',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'shop/goods/virtual',
                        'router_path' => 'shop/goods/virtual_edit',
                        'view_path' => 'goods/virtual_edit',
                        'methods' => 'post',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '商品编辑',
                        'menu_key' => 'shop_goods_virtual_edit',
                        'menu_short_name' => '商品编辑',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'shop/goods/virtual/<id>',
                        'router_path' => 'shop/goods/virtual_edit',
                        'view_path' => 'goods/virtual_edit',
                        'methods' => 'put',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '商品删除',
                        'menu_key' => 'shop_goods_delete',
                        'menu_short_name' => '商品删除',
                        'menu_type' => '2',
                        'icon' => '',
                        'api_url' => 'shop/goods/delete',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '商品排序号修改',
                        'menu_key' => 'shop_goods_sort_edit',
                        'menu_short_name' => '商品排序号修改',
                        'menu_type' => '2',
                        'icon' => '',
                        'api_url' => 'shop/goods/sort',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '商品上下架状态修改',
                        'menu_key' => 'shop_goods_status_edit',
                        'menu_short_name' => '商品上下架状态修改',
                        'menu_type' => '2',
                        'icon' => '',
                        'api_url' => 'shop/goods/status',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '商品复制',
                        'menu_key' => 'shop_goods_copy',
                        'menu_short_name' => '商品复制',
                        'menu_type' => '2',
                        'icon' => '',
                        'api_url' => 'shop/goods/copy/<goods_id>',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '商品规格列表库存编辑',
                        'menu_key' => 'shop_goods_sku_stock_edit',
                        'menu_short_name' => '商品规格列表库存编辑',
                        'menu_type' => '2',
                        'icon' => '',
                        'api_url' => 'shop/goods/sku/stock',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '商品规格列表价格编辑',
                        'menu_key' => 'shop_goods_sku_price_edit',
                        'menu_short_name' => '商品规格列表价格编辑',
                        'menu_type' => '2',
                        'icon' => '',
                        'api_url' => 'shop/goods/sku/price',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '商品规格列表会员价格编辑',
                        'menu_key' => 'shop_goods_sku_member_price_edit',
                        'menu_short_name' => '商品规格列表会员价格编辑',
                        'menu_type' => '2',
                        'icon' => '',
                        'api_url' => 'shop/goods/sku/member_price',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '删除参数模板',
                        'menu_key' => 'shop_goods_attr_delete',
                        'menu_short_name' => '删除参数模板',
                        'menu_type' => '2',
                        'icon' => '',
                        'api_url' => 'shop/goods/attr/<id>',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'delete',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '编辑参数模板',
                        'menu_key' => 'shop_goods_attr_edit',
                        'menu_short_name' => '编辑参数模板',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'shop/goods/attr/<id>',
                        'router_path' => 'shop/goods/attr_edit',
                        'view_path' => 'goods/attr_edit',
                        'methods' => 'put',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '添加参数模板',
                        'menu_key' => 'shop_goods_attr_add',
                        'menu_short_name' => '添加参数模板',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'shop/goods/attr',
                        'router_path' => 'shop/goods/attr_edit',
                        'view_path' => 'goods/attr_edit',
                        'methods' => 'post',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '商品添加',
                        'menu_key' => 'shop_goods_real_add',
                        'menu_short_name' => '商品添加',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'shop/goods',
                        'router_path' => 'shop/goods/real_edit',
                        'view_path' => 'goods/real_edit',
                        'methods' => 'post',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '商品编辑',
                        'menu_key' => 'shop_goods_real_edit',
                        'menu_short_name' => '商品编辑',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'shop/goods/<id>',
                        'router_path' => 'shop/goods/real_edit',
                        'view_path' => 'goods/real_edit',
                        'methods' => 'put',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                ],
            ],
            [
                'menu_name' => '订单管理',
                'menu_key' => 'shop_order',
                'menu_short_name' => '订单',
                'menu_type' => '0',
                'icon' => 'iconfont icondingdanguanli',
                'api_url' => '',
                'router_path' => '',
                'view_path' => '',
                'methods' => 'get',
                'sort' => '80',
                'status' => '1',
                'is_show' => '1',
                'children' => [
                    [
                        'menu_name' => '商城订单',
                        'menu_key' => 'shop_order_list',
                        'menu_short_name' => '商城订单',
                        'menu_type' => '1',
                        'icon' => 'iconfont icona-dingdanliebiao',
                        'api_url' => 'shop/order/list',
                        'router_path' => 'shop/order/index',
                        'view_path' => 'order/list',
                        'methods' => 'get',
                        'sort' => '100',
                        'status' => '1',
                        'is_show' => '1',
                        'children' => [
                            [
                                'menu_name' => '订单关闭',
                                'menu_key' => 'shop_order_close',
                                'menu_short_name' => '订单关闭',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/order/close/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '订单改价',
                                'menu_key' => 'shop_order_edit_price',
                                'menu_short_name' => '订单改价',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/order/edit_price',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '订单配送修改',
                                'menu_key' => 'shop_order_edit_delivery',
                                'menu_short_name' => '订单配送修改',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/order/edit_delivery',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '订单发货',
                                'menu_key' => 'shop_order_delivery',
                                'menu_short_name' => '订单发货',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/order/delivery',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '订单项发货',
                                'menu_key' => 'shop_order_goods_delivery',
                                'menu_short_name' => '订单项发货',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/order/goods/delivery/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '商家留言',
                                'menu_key' => 'shop_order_remark',
                                'menu_short_name' => '商家留言',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/order/shop_remark',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '订单完成',
                                'menu_key' => 'shop_order_finish',
                                'menu_short_name' => '订单完成',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/order/finish/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '退款售后',
                        'menu_key' => 'shop_order_refund',
                        'menu_short_name' => '退款售后',
                        'menu_type' => '1',
                        'icon' => 'iconfont icondingdanweiquan',
                        'api_url' => '',
                        'router_path' => 'shop/order/refund',
                        'view_path' => 'order/refund',
                        'methods' => 'get',
                        'sort' => '89',
                        'status' => '1',
                        'is_show' => '1',
                        'children' => [
                            [
                                'menu_name' => '退款审核',
                                'menu_key' => 'shop_order_refund_audit',
                                'menu_short_name' => '退款审核',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/order/refund/audit/<order_refund_no>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '退款收货审核',
                                'menu_key' => 'shop_order_refund_delivery',
                                'menu_short_name' => '退款收货审核',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/order/refund/delivery/<order_refund_no>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '批量发货',
                        'menu_key' => 'shop_order_batch_delivery',
                        'menu_short_name' => '批量发货',
                        'menu_type' => '1',
                        'icon' => 'iconfont icona-dingdanliebiao',
                        'api_url' => '',
                        'router_path' => 'shop/order/batch/delivery',
                        'view_path' => 'order/batch_delivery',
                        'methods' => 'get',
                        'sort' => '88',
                        'status' => '1',
                        'is_show' => '1',
                    ],
                    [
                        'menu_name' => '发票管理',
                        'menu_key' => 'shop_invoice',
                        'menu_short_name' => '发票管理',
                        'menu_type' => '1',
                        'icon' => 'iconfont iconfapiaoguanli',
                        'api_url' => '',
                        'router_path' => 'shop/order/invoice',
                        'view_path' => 'order/invoice',
                        'methods' => 'get',
                        'sort' => '87',
                        'status' => '1',
                        'is_show' => '1',
                        'children' => [
                            [
                                'menu_name' => '开票',
                                'menu_key' => 'shop_invoice_open',
                                'menu_short_name' => '开票',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/invoice/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '商品评价',
                        'menu_key' => 'shop_goods_evaluate',
                        'menu_short_name' => '商品评价',
                        'menu_type' => '1',
                        'icon' => 'iconfont iconshangpinpinglun',
                        'api_url' => 'shop/goods/evaluate',
                        'router_path' => 'shop/order/evaluate',
                        'view_path' => 'goods/evaluate',
                        'methods' => 'get',
                        'sort' => '86',
                        'status' => '1',
                        'is_show' => '1',
                        'children' => [
                            [
                                'menu_name' => '商品评价删除',
                                'menu_key' => 'shop_goods_evaluate_delete',
                                'menu_short_name' => '商品评价删除',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/evaluate/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'delete',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '商品评价回复',
                                'menu_key' => 'shop_goods_evaluate_reply',
                                'menu_short_name' => '商品评价回复',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/evaluate/reply/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '商品评价通过',
                                'menu_key' => 'shop_goods_evaluate_adopt',
                                'menu_short_name' => '商品评价通过',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/evaluate/adopt/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '商品评价拒绝',
                                'menu_key' => 'shop_goods_evaluate_refuse',
                                'menu_short_name' => '商品评价拒绝',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/evaluate/refuse/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '商品评价置顶',
                                'menu_key' => 'shop_goods_evaluate_topping',
                                'menu_short_name' => '商品评价置顶',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/evaluate/topping/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '商品评价取消置顶',
                                'menu_key' => 'shop_goods_evaluate_cancel_topping',
                                'menu_short_name' => '商品评价取消置顶',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/goods/evaluate/cancel_topping/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '订单设置',
                        'menu_key' => 'shop_config_order',
                        'menu_short_name' => '订单设置',
                        'menu_type' => '1',
                        'icon' => 'iconfont iconjiaoyishezhi',
                        'api_url' => 'shop/order/config',
                        'router_path' => 'shop/order/order/config',
                        'view_path' => 'order/config',
                        'methods' => '',
                        'sort' => '85',
                        'status' => '1',
                        'is_show' => '1',
                        'children' => [
                            [
                                'menu_name' => '订单设置编辑',
                                'menu_key' => 'shop_config_order_set',
                                'menu_short_name' => '订单设置编辑',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/order/config',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'post',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '商家地址库',
                        'menu_key' => 'shop_address',
                        'menu_short_name' => '商家地址库',
                        'menu_type' => '1',
                        'icon' => 'iconfont iconshangjiadizhiku',
                        'api_url' => '',
                        'router_path' => 'shop/order/address',
                        'view_path' => 'address/list',
                        'methods' => 'get',
                        'sort' => '84',
                        'status' => '1',
                        'is_show' => '1',
                        'children' => [
                            [
                                'menu_name' => '删除商家地址库',
                                'menu_key' => 'shop_address_delete',
                                'menu_short_name' => '删除商家地址库',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/shop_address/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'delete',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '配送设置',
                        'menu_key' => 'shop_config_delivery',
                        'menu_short_name' => '配送设置',
                        'menu_type' => '1',
                        'icon' => 'iconfont iconpaisongshezhi',
                        'api_url' => '',
                        'router_path' => 'shop/order/delivery',
                        'view_path' => 'delivery/config',
                        'methods' => '',
                        'sort' => '83',
                        'status' => '1',
                        'is_show' => '1',
                        'children' => [
                            [
                                'menu_name' => '配送设置编辑',
                                'menu_key' => 'shop_config_delivery_set',
                                'menu_short_name' => '配送设置编辑',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/delivery/setConfig',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '电子面单',
                        'menu_key' => 'shop_delivery_electronic_sheet',
                        'menu_short_name' => '电子面单',
                        'menu_type' => '1',
                        'icon' => 'iconfont icondiscount',
                        'api_url' => '',
                        'router_path' => 'shop/delivery/electronic_sheet',
                        'view_path' => 'delivery/electronic_sheet',
                        'methods' => 'get',
                        'sort' => '82',
                        'status' => '1',
                        'is_show' => '1',
                        'children' => [
                            [
                                'menu_name' => '删除电子面单模板',
                                'menu_key' => 'shop_delivery_electronic_sheet_del',
                                'menu_short_name' => '删除电子面单模板',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/electronic_sheet',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'delete',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '添加评价',
                        'menu_key' => 'shop_goods_evaluate_add',
                        'menu_short_name' => '添加评价',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'shop/goods/evaluate',
                        'router_path' => 'shop/order/evaluate/add',
                        'view_path' => 'goods/evaluate_edit',
                        'methods' => 'get',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '添加/编辑地址库',
                        'menu_key' => 'shop_address_add_edit',
                        'menu_short_name' => '添加/编辑地址库',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => '',
                        'router_path' => 'shop/order/address/edit',
                        'view_path' => 'address/edit',
                        'methods' => 'get',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                        'children' => [
                            [
                                'menu_name' => '地址库添加',
                                'menu_key' => 'shop_address_add',
                                'menu_short_name' => '地址库添加',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/shop_address',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'post',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '地址库编辑',
                                'menu_key' => 'shop_address_edit',
                                'menu_short_name' => '地址库编辑',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/shop_address/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '物流公司',
                        'menu_key' => 'shop_config_delivery_company',
                        'menu_short_name' => '物流公司',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'shop/delivery/company',
                        'router_path' => 'shop/order/delivery/company',
                        'view_path' => 'delivery/company',
                        'methods' => 'get',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                        'children' => [
                            [
                                'menu_name' => '物流公司删除',
                                'menu_key' => 'shop_config_delivery_company_delete',
                                'menu_short_name' => '物流公司删除',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/delivery/company/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'delete',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '添加物流公司',
                        'menu_key' => 'shop_config_delivery_company_add',
                        'menu_short_name' => '添加物流公司',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'shop/delivery/company',
                        'router_path' => 'shop/order/delivery/company_add',
                        'view_path' => 'delivery/company_edit',
                        'methods' => 'post',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '编辑物流公司',
                        'menu_key' => 'shop_config_delivery_company_edit',
                        'menu_short_name' => '编辑物流公司',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'shop/delivery/company',
                        'router_path' => 'shop/order/delivery/company_edit',
                        'view_path' => 'delivery/company_edit',
                        'methods' => 'put',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '同城配送',
                        'menu_key' => 'shop_config_delivery_local',
                        'menu_short_name' => '同城配送',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => '',
                        'router_path' => 'shop/order/delivery/local',
                        'view_path' => 'delivery/local',
                        'methods' => 'get',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                        'children' => [
                            [
                                'menu_name' => '同城配送设置',
                                'menu_key' => 'shop_config_delivery_local_set',
                                'menu_short_name' => '同城配送设置',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/local',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '运费模版',
                        'menu_key' => 'shop_config_delivery_shipping_template',
                        'menu_short_name' => '运费模版',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'shop/shipping/template',
                        'router_path' => 'shop/order/shipping/template',
                        'view_path' => 'delivery/template',
                        'methods' => 'get',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                        'children' => [
                            [
                                'menu_name' => '运费模版删除',
                                'menu_key' => 'shop_config_delivery_shipping_template_delete',
                                'menu_short_name' => '运费模版列表',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/shipping/template/<template_id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'delete',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '运费模版添加/编辑',
                        'menu_key' => 'shop_config_delivery_shipping_template_add_edit',
                        'menu_short_name' => '运费模版添加/编辑',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => '',
                        'router_path' => 'shop/order/shipping/template_edit',
                        'view_path' => 'delivery/template_edit',
                        'methods' => 'get',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                        'children' => [
                            [
                                'menu_name' => '运费模版添加',
                                'menu_key' => 'shop_config_delivery_shipping_template_add',
                                'menu_short_name' => '运费模版添加',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/shipping/template',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'post',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '运费模版编辑',
                                'menu_key' => 'shop_config_delivery_shipping_template_edit',
                                'menu_short_name' => '运费模版编辑',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/shipping/template/<template_id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '配送员',
                        'menu_key' => 'shop_config_delivery_staff',
                        'menu_short_name' => '配送员',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => '',
                        'router_path' => 'shop/order/delivery/staff',
                        'view_path' => 'delivery/staff',
                        'methods' => '',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                        'children' => [
                            [
                                'menu_name' => '配送员添加',
                                'menu_key' => 'shop_config_delivery_staff_add',
                                'menu_short_name' => '配送员添加',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/delivery/staff',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'post',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '配送员编辑',
                                'menu_key' => 'shop_config_delivery_staff_edit',
                                'menu_short_name' => '配送员编辑',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/delivery/staff/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '配送员删除',
                                'menu_key' => 'shop_config_delivery_staff_delete',
                                'menu_short_name' => '配送员删除',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/delivery/staff/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'delete',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '订单详情',
                        'menu_key' => 'shop_order_detail',
                        'menu_short_name' => '订单详情',
                        'menu_type' => '1',
                        'icon' => 'iconfont iconshangpinguanli',
                        'api_url' => 'shop/order/detail/<id>',
                        'router_path' => 'shop/order/detail',
                        'view_path' => 'order/detail',
                        'methods' => 'get',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '物流跟踪',
                        'menu_key' => 'shop_config_delivery_search',
                        'menu_short_name' => '物流跟踪',
                        'menu_type' => '1',
                        'icon' => 'iconfont iconwuliugenzong',
                        'api_url' => '',
                        'router_path' => 'shop/order/delivery/search',
                        'view_path' => 'delivery/search',
                        'methods' => 'get',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                        'children' => [
                            [
                                'menu_name' => '物流跟踪设置',
                                'menu_key' => 'shop_config_delivery_search_set',
                                'menu_short_name' => '物流跟踪设置',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/delivery/search',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'post',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '自提点',
                        'menu_key' => 'shop_config_shop_delivery_store',
                        'menu_short_name' => '自提点',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'shop/delivery/store',
                        'router_path' => 'shop/order/delivery/store',
                        'view_path' => 'delivery/store',
                        'methods' => 'get',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                        'children' => [
                            [
                                'menu_name' => '自提点删除',
                                'menu_key' => 'shop_config_delete_delivery_store',
                                'menu_short_name' => '自提点删除',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/delivery/store/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'delete',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '自提点添加/编辑',
                        'menu_key' => 'shop_config_add_edit_delivery_store',
                        'menu_short_name' => '自提点添加/编辑',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => '',
                        'router_path' => 'shop/order/delivery/store/edit',
                        'view_path' => 'delivery/store_edit',
                        'methods' => 'post',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                        'children' => [
                            [
                                'menu_name' => '自提点添加',
                                'menu_key' => 'shop_config_add_delivery_store',
                                'menu_short_name' => '自提点添加',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/delivery/store',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'post',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                            [
                                'menu_name' => '自提点编辑',
                                'menu_key' => 'shop_config_edit_delivery_store',
                                'menu_short_name' => '自提点编辑',
                                'menu_type' => '2',
                                'icon' => '',
                                'api_url' => 'shop/delivery/store/<id>',
                                'router_path' => '',
                                'view_path' => '',
                                'methods' => 'put',
                                'sort' => '0',
                                'status' => '1',
                                'is_show' => '0',
                            ],
                        ],
                    ],
                    [
                        'menu_name' => '售后详情',
                        'menu_key' => 'shop_order_refund_detail',
                        'menu_short_name' => '售后详情',
                        'menu_type' => '1',
                        'icon' => 'iconfont iconshangpinguanli',
                        'api_url' => '',
                        'router_path' => 'shop/order/refund/detail',
                        'view_path' => 'order/refund_detail',
                        'methods' => 'get',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '电子面单设置',
                        'menu_key' => 'shop_delivery_electronic_sheet_config',
                        'menu_short_name' => '电子面单设置',
                        'menu_type' => '1',
                        'icon' => 'iconfont icondiscount',
                        'api_url' => 'shop/electronic_sheet/config',
                        'router_path' => 'shop/delivery/electronic_sheet/config',
                        'view_path' => 'delivery/electronic_sheet_config',
                        'methods' => 'get',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '添加模板',
                        'menu_key' => 'shop_delivery_electronic_sheet_add',
                        'menu_short_name' => '添加电子面单模板',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'shop/electronic_sheet',
                        'router_path' => 'shop/delivery/electronic_sheet_add',
                        'view_path' => 'delivery/electronic_sheet_edit',
                        'methods' => 'post',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '编辑模板',
                        'menu_key' => 'shop_delivery_electronic_sheet_edit',
                        'menu_short_name' => '编辑电子面单模板',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'shop/electronic_sheet/<id>',
                        'router_path' => 'shop/delivery/electronic_sheet_edit',
                        'view_path' => 'delivery/electronic_sheet_edit',
                        'methods' => 'put',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                ],
            ],
            [
                'menu_name' => '数据统计',
                'menu_key' => 'shop_stat',
                'menu_short_name' => '数据',
                'menu_type' => '0',
                'icon' => 'iconfont iconshujutongji',
                'api_url' => '',
                'router_path' => '',
                'view_path' => '',
                'methods' => 'get',
                'sort' => '70',
                'status' => '1',
                'is_show' => '0',
                'children' => [
                    [
                        'menu_name' => '数据概况',
                        'menu_key' => 'shop_stat_index',
                        'menu_short_name' => '数据概况',
                        'menu_type' => '1',
                        'icon' => 'iconfont icona-dingdanliebiao',
                        'api_url' => 'shop/stat/index',
                        'router_path' => 'shop/stat/index',
                        'view_path' => 'stat/index',
                        'methods' => 'get',
                        'sort' => '90',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '会员数据',
                        'menu_key' => 'shop_stat_member',
                        'menu_short_name' => '会员数据',
                        'menu_type' => '1',
                        'icon' => 'iconfont icona-dingdanliebiao',
                        'api_url' => 'shop/stat/member',
                        'router_path' => 'shop/stat/member',
                        'view_path' => 'stat/member',
                        'methods' => 'get',
                        'sort' => '89',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '交易数据',
                        'menu_key' => 'shop_stat_order',
                        'menu_short_name' => '交易数据',
                        'menu_type' => '1',
                        'icon' => 'iconfont icona-dingdanliebiao',
                        'api_url' => '',
                        'router_path' => 'shop/stat/order',
                        'view_path' => 'stat/order',
                        'methods' => 'get',
                        'sort' => '88',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '流量数据',
                        'menu_key' => 'shop_stat_visit',
                        'menu_short_name' => '流量数据',
                        'menu_type' => '1',
                        'icon' => 'iconfont icona-dingdanliebiao',
                        'api_url' => 'shop/stat/visit',
                        'router_path' => 'shop/stat/visit',
                        'view_path' => 'stat/visit',
                        'methods' => 'get',
                        'sort' => '87',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                ],
            ],
        ],
    ],
    [
        'menu_name' => '优惠券',
        'menu_key' => 'shop_goods_coupon_list',
        'menu_short_name' => '优惠券',
        'parent_key' => 'active',
        'menu_type' => '1',
        'icon' => 'iconfont iconyouhuiquan',
        'api_url' => '',
        'router_path' => 'shop/marketing/coupon/list',
        'view_path' => 'marketing/coupon/list',
        'methods' => 'get',
        'sort' => '90',
        'status' => '1',
        'is_show' => '1',
        'children' => [
            [
                'menu_name' => '优惠券删除',
                'menu_key' => 'shop_goods_coupon_delete',
                'menu_short_name' => '优惠券删除',
                'menu_type' => '2',
                'icon' => '',
                'api_url' => 'shop/goods/coupon/delete',
                'router_path' => '',
                'view_path' => '',
                'methods' => 'post',
                'sort' => '0',
                'status' => '1',
                'is_show' => '0',
            ],
            [
                'menu_name' => '优惠券设置状态',
                'menu_key' => 'shop_goods_coupon_set_status',
                'menu_short_name' => '优惠券设置状态',
                'menu_type' => '2',
                'icon' => '',
                'api_url' => 'shop/goods/coupon/setstatus/<status>',
                'router_path' => '',
                'view_path' => '',
                'methods' => 'put',
                'sort' => '0',
                'status' => '1',
                'is_show' => '0',
            ],
            [
                'menu_name' => '发券记录',
                'menu_key' => 'shop_goods_coupon_send_list',
                'menu_short_name' => '发券记录',
                'menu_type' => '2',
                'icon' => '',
                'api_url' => 'shop/goods/coupon/send/pages/<coupon_id>',
                'router_path' => 'shop/marketing/coupon/send_list',
                'view_path' => 'marketing/coupon/send_list',
                'methods' => 'get',
                'sort' => '0',
                'status' => '1',
                'is_show' => '0',
            ],
            [
                'menu_name' => '发券',
                'menu_key' => 'shop_goods_coupon_send',
                'menu_short_name' => '发券',
                'menu_type' => '2',
                'icon' => '',
                'api_url' => 'shop/goods/coupon/send/<coupon_id>',
                'router_path' => 'shop/marketing/coupon/send',
                'view_path' => 'marketing/coupon/send',
                'methods' => 'post',
                'sort' => '0',
                'status' => '1',
                'is_show' => '0',
            ],
        ],
    ],
    [
        'menu_name' => '满减送',
        'menu_key' => 'shop_goods_manjian',
        'menu_short_name' => '满减送',
        'parent_key' => 'active',
        'menu_type' => '1',
        'icon' => 'iconfont icondiscount',
        'api_url' => 'shop/manjian',
        'router_path' => 'shop/marketing/manjian/list',
        'view_path' => 'marketing/manjian/list',
        'methods' => 'get',
        'sort' => '89',
        'status' => '1',
        'is_show' => '1',
        'children' => [
            [
                'menu_name' => '活动关闭',
                'menu_key' => 'shop_goods_manjian_close',
                'menu_short_name' => '活动关闭',
                'menu_type' => '2',
                'icon' => '',
                'api_url' => 'shop/manjian/close/<id>',
                'router_path' => '',
                'view_path' => '',
                'methods' => 'put',
                'sort' => '0',
                'status' => '1',
                'is_show' => '0',
            ],
            [
                'menu_name' => '活动删除',
                'menu_key' => 'shop_goods_manjian_delete',
                'menu_short_name' => '活动删除',
                'menu_type' => '2',
                'icon' => '',
                'api_url' => 'shop/manjian/<id>',
                'router_path' => '',
                'view_path' => '',
                'methods' => 'delete',
                'sort' => '0',
                'status' => '1',
                'is_show' => '0',
            ],
        ],
    ],
    [
        'menu_name' => '限时折扣',
        'menu_key' => 'shop_goods_discount',
        'menu_short_name' => '限时折扣',
        'parent_key' => 'active',
        'menu_type' => '1',
        'icon' => 'iconfont icondiscount',
        'api_url' => '',
        'router_path' => '',
        'view_path' => '',
        'methods' => 'get',
        'sort' => '88',
        'status' => '1',
        'is_show' => '1',
        'children' => [
            [
                'menu_name' => '活动列表',
                'menu_key' => 'shop_goods_discount_list',
                'menu_short_name' => '活动列表',
                'menu_type' => '1',
                'icon' => 'iconfont icondiscount',
                'api_url' => 'shop/active/discount',
                'router_path' => 'shop/marketing/discount/list',
                'view_path' => 'marketing/discount/list',
                'methods' => 'get',
                'sort' => '90',
                'status' => '1',
                'is_show' => '1',
                'children' => [
                    [
                        'menu_name' => '活动关闭',
                        'menu_key' => 'shop_goods_discount_close',
                        'menu_short_name' => '活动关闭',
                        'menu_type' => '2',
                        'icon' => '',
                        'api_url' => 'shop/active/discount/close/<active_id>',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '活动删除',
                        'menu_key' => 'shop_goods_discount_delete',
                        'menu_short_name' => '活动删除',
                        'menu_type' => '2',
                        'icon' => '',
                        'api_url' => 'shop/active/discount/<active_id>',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'delete',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                ],
            ],
            [
                'menu_name' => '活动设置',
                'menu_key' => 'shop_goods_discount_config',
                'menu_short_name' => '活动设置',
                'menu_type' => '1',
                'icon' => 'iconfont icondiscount',
                'api_url' => '',
                'router_path' => 'shop/marketing/discount/config',
                'view_path' => 'marketing/discount/config',
                'methods' => 'get',
                'sort' => '80',
                'status' => '1',
                'is_show' => '1',
                'children' => [
                    [
                        'menu_name' => '活动设置编辑',
                        'menu_key' => 'shop_goods_discount_config_set',
                        'menu_short_name' => '活动设置编辑',
                        'menu_type' => '2',
                        'icon' => '',
                        'api_url' => 'shop/active/discount/config',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                ],
            ],
            [
                'menu_name' => '活动详情',
                'menu_key' => 'shop_goods_discount_detail',
                'menu_short_name' => '活动详情',
                'menu_type' => '1',
                'icon' => '',
                'api_url' => 'shop/active/discount/<active_id>',
                'router_path' => 'shop/marketing/discount/detail',
                'view_path' => 'marketing/discount/detail',
                'methods' => 'get',
                'sort' => '0',
                'status' => '1',
                'is_show' => '0',
            ],
            [
                'menu_name' => '编辑限时折扣',
                'menu_key' => 'shop_goods_discount_edit',
                'menu_short_name' => '编辑限时折扣',
                'menu_type' => '1',
                'icon' => '',
                'api_url' => 'shop/active/discount/<active_id>',
                'router_path' => 'shop/marketing/discount/edit',
                'view_path' => 'marketing/discount/edit',
                'methods' => 'put',
                'sort' => '0',
                'status' => '1',
                'is_show' => '0',
            ],
            [
                'menu_name' => '添加限时折扣',
                'menu_key' => 'shop_goods_discount_add',
                'menu_short_name' => '添加限时折扣',
                'menu_type' => '1',
                'icon' => '',
                'api_url' => 'shop/active/discount',
                'router_path' => 'shop/marketing/discount/add',
                'view_path' => 'marketing/discount/add',
                'methods' => 'post',
                'sort' => '0',
                'status' => '1',
                'is_show' => '0',
            ],
        ],
    ],
    [
        'menu_name' => '积分商城',
        'menu_key' => 'shop_goods_point_exchange',
        'menu_short_name' => '积分商城',
        'parent_key' => 'active',
        'menu_type' => '0',
        'icon' => 'iconfont iconpointshop',
        'api_url' => '',
        'router_path' => '',
        'view_path' => '',
        'methods' => 'get',
        'sort' => '87',
        'status' => '1',
        'is_show' => '1',
        'children' => [
            [
                'menu_name' => '积分商品',
                'menu_key' => 'shop_point_goods_list',
                'menu_short_name' => '积分商品',
                'menu_type' => '1',
                'icon' => 'iconfont iconpointgoods',
                'api_url' => 'shop/active/exchange',
                'router_path' => 'shop/marketing/exchange/goods_list',
                'view_path' => 'marketing/exchange/goods_list',
                'methods' => 'get',
                'sort' => '90',
                'status' => '1',
                'is_show' => '1',
                'children' => [
                    [
                        'menu_name' => '修改上下架状态',
                        'menu_key' => 'shop_point_goods_status_edit',
                        'menu_short_name' => '修改上下架状态',
                        'menu_type' => '2',
                        'icon' => '',
                        'api_url' => 'shop/active/exchange/status/<id>',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '删除积分商品',
                        'menu_key' => 'shop_point_goods_delete',
                        'menu_short_name' => '删除积分商品',
                        'menu_type' => '2',
                        'icon' => '',
                        'api_url' => 'shop/active/exchange/<id>',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'delete',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '修改排序号',
                        'menu_key' => 'shop_point_goods_sort_edit',
                        'menu_short_name' => '修改排序号',
                        'menu_type' => '2',
                        'icon' => '',
                        'api_url' => 'shop/active/exchange/sort/<id>',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                ],
            ],
            [
                'menu_name' => '兑换记录',
                'menu_key' => 'shop_point_exchange_list',
                'menu_short_name' => '兑换记录',
                'menu_type' => '1',
                'icon' => 'iconfont iconpointexchange',
                'api_url' => '',
                'router_path' => 'shop/marketing/exchange/order_list',
                'view_path' => 'marketing/exchange/order_list',
                'methods' => 'get',
                'sort' => '89',
                'status' => '1',
                'is_show' => '1',
            ],
            [
                'menu_name' => '添加商品',
                'menu_key' => 'shop_point_goods_add',
                'menu_short_name' => '添加商品',
                'menu_type' => '1',
                'icon' => '',
                'api_url' => 'shop/active/exchange',
                'router_path' => 'shop/marketing/exchange/goods_add',
                'view_path' => 'marketing/exchange/goods_add',
                'methods' => 'post',
                'sort' => '0',
                'status' => '1',
                'is_show' => '0',
            ],
            [
                'menu_name' => '编辑商品',
                'menu_key' => 'shop_point_goods_edit',
                'menu_short_name' => '编辑商品',
                'menu_type' => '1',
                'icon' => '',
                'api_url' => 'shop/active/exchange/<id>',
                'router_path' => 'shop/marketing/exchange/goods_edit',
                'view_path' => 'marketing/exchange/goods_edit',
                'methods' => 'put',
                'sort' => '0',
                'status' => '1',
                'is_show' => '0',
            ],
        ],
    ],
    [
        'menu_name' => '新人专享',
        'menu_key' => 'shop_goods_newcomer_discount',
        'menu_short_name' => '新人专享',
        'parent_key' => 'active',
        'menu_type' => '1',
        'icon' => 'iconfont icondiscount',
        'api_url' => '',
        'router_path' => '',
        'view_path' => '',
        'methods' => 'get',
        'sort' => '86',
        'status' => '1',
        'is_show' => '1',
        'children' => [
            [
                'menu_name' => '新人设置',
                'menu_key' => 'shop_goods_newcomer_discount_config',
                'menu_short_name' => '新人设置',
                'menu_type' => '1',
                'icon' => 'iconfont icondiscount',
                'api_url' => 'shop/active/newcomer/config',
                'router_path' => 'shop/marketing/newcomer/config',
                'view_path' => 'marketing/newcomer/config',
                'methods' => 'put',
                'sort' => '90',
                'status' => '1',
                'is_show' => '1',
            ],
            [
                'menu_name' => '购买记录',
                'menu_key' => 'shop_goods_newcomer_discount_order_list',
                'menu_short_name' => '购买记录',
                'menu_type' => '1',
                'icon' => 'iconfont icondiscount',
                'api_url' => 'shop/active/newcomer/order',
                'router_path' => 'shop/marketing/newcomer/order_list',
                'view_path' => 'marketing/newcomer/order_list',
                'methods' => 'get',
                'sort' => '80',
                'status' => '1',
                'is_show' => '1',
            ],
        ],
    ],
    [
        'menu_name' => '商品榜单',
        'menu_key' => 'shop_goods_rank',
        'menu_short_name' => '商品榜单',
        'parent_key' => 'active',
        'menu_type' => '1',
        'icon' => 'iconfont icondiscount',
        'api_url' => '',
        'router_path' => '',
        'view_path' => '',
        'methods' => 'get',
        'sort' => '85',
        'status' => '1',
        'is_show' => '1',
        'children' => [
            [
                'menu_name' => '榜单设置',
                'menu_key' => 'shop_goods_rank_config',
                'menu_short_name' => '榜单设置',
                'menu_type' => '1',
                'icon' => 'iconfont icondiscount',
                'api_url' => 'shop/good/rank/config',
                'router_path' => 'shop/marketing/goods_rank/config',
                'view_path' => 'marketing/goods_rank/config',
                'methods' => 'get',
                'sort' => '90',
                'status' => '1',
                'is_show' => '1',
            ],
            [
                'menu_name' => '榜单管理',
                'menu_key' => 'shop_goods_rank_list',
                'menu_short_name' => '榜单管理',
                'menu_type' => '1',
                'icon' => 'iconfont icondiscount',
                'api_url' => 'shop/good/rank',
                'router_path' => 'shop/marketing/goods_rank/list',
                'view_path' => 'marketing/goods_rank/list',
                'methods' => 'get',
                'sort' => '80',
                'status' => '1',
                'is_show' => '1',
                'children' => [
                    [
                        'menu_name' => '删除榜单',
                        'menu_key' => 'shop_goods_rank_delete',
                        'menu_short_name' => '删除榜单',
                        'menu_type' => '2',
                        'icon' => '',
                        'api_url' => 'shop/good/rank/<id>',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'delete',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '批量删除榜单',
                        'menu_key' => 'shop_goods_rank_batch_delete',
                        'menu_short_name' => '批量删除榜单',
                        'menu_type' => '2',
                        'icon' => '',
                        'api_url' => 'shop/good/rank/batchdelete',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '榜单排序号修改',
                        'menu_key' => 'shop_goods_rank_sort_edit',
                        'menu_short_name' => '榜单排序号修改',
                        'menu_type' => '2',
                        'icon' => '',
                        'api_url' => 'shop/good/rank/sort',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => '0',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                ],
            ],
            [
                'menu_name' => '榜单添加',
                'menu_key' => 'shop_goods_rank_add',
                'menu_short_name' => '榜单添加',
                'menu_type' => '1',
                'icon' => '',
                'api_url' => 'shop/good/rank',
                'router_path' => 'shop/marketing/goods_rank/edit',
                'view_path' => 'marketing/goods_rank/edit',
                'methods' => 'post',
                'sort' => '0',
                'status' => '1',
                'is_show' => '0',
            ],
            [
                'menu_name' => '榜单编辑',
                'menu_key' => 'shop_goods_rank_edit',
                'menu_short_name' => '榜单编辑',
                'menu_type' => '1',
                'icon' => '',
                'api_url' => 'shop/good/rank/<id>',
                'router_path' => 'shop/marketing/goods_rank/edit',
                'view_path' => 'marketing/goods_rank/edit',
                'methods' => 'put',
                'sort' => '0',
                'status' => '1',
                'is_show' => '0',
            ],
        ],
    ],
    [
        'menu_name' => '优惠券添加',
        'menu_key' => 'shop_goods_coupon_add',
        'menu_short_name' => '优惠券添加',
        'parent_key' => 'active',
        'menu_type' => '1',
        'icon' => '',
        'api_url' => 'shop/goods/coupon',
        'router_path' => 'shop/marketing/coupon/add',
        'view_path' => 'marketing/coupon/add',
        'methods' => 'post',
        'sort' => '0',
        'status' => '1',
        'is_show' => '0',
    ],
    [
        'menu_name' => '优惠券编辑',
        'menu_key' => 'shop_goods_coupon_edit',
        'menu_short_name' => '优惠券编辑',
        'parent_key' => 'active',
        'menu_type' => '1',
        'icon' => '',
        'api_url' => 'shop/goods/coupon/edit/<id>',
        'router_path' => 'shop/marketing/coupon/edit',
        'view_path' => 'marketing/coupon/edit',
        'methods' => 'put',
        'sort' => '0',
        'status' => '1',
        'is_show' => '0',
    ],
    [
        'menu_name' => '优惠券领取记录',
        'menu_key' => 'shop_goods_coupon_collection',
        'menu_short_name' => '优惠券领取记录',
        'parent_key' => 'active',
        'menu_type' => '1',
        'icon' => '',
        'api_url' => 'shop/goods/coupon/records',
        'router_path' => 'shop/marketing/coupon/collection',
        'view_path' => 'marketing/coupon/collection',
        'methods' => 'get',
        'sort' => '0',
        'status' => '1',
        'is_show' => '0',
    ],
    [
        'menu_name' => '活动详情',
        'menu_key' => 'shop_goods_manjian_detail',
        'menu_short_name' => '活动详情',
        'parent_key' => 'active',
        'menu_type' => '1',
        'icon' => '',
        'api_url' => 'shop/manjian/<id>',
        'router_path' => 'shop/marketing/manjian/detail',
        'view_path' => 'marketing/manjian/detail',
        'methods' => 'get',
        'sort' => '0',
        'status' => '1',
        'is_show' => '0',
    ],
    [
        'menu_name' => '编辑满减送活动',
        'menu_key' => 'shop_goods_manjian_edit',
        'menu_short_name' => '编辑满减送活动',
        'parent_key' => 'active',
        'menu_type' => '1',
        'icon' => '',
        'api_url' => 'shop/manjian/<id>',
        'router_path' => 'shop/marketing/manjian/edit',
        'view_path' => 'marketing/manjian/edit',
        'methods' => 'put',
        'sort' => '0',
        'status' => '1',
        'is_show' => '0',
    ],
    [
        'menu_name' => '添加满减送活动',
        'menu_key' => 'shop_goods_manjian_add',
        'menu_short_name' => '添加满减送活动',
        'parent_key' => 'active',
        'menu_type' => '1',
        'icon' => '',
        'api_url' => 'shop/manjian',
        'router_path' => 'shop/marketing/manjian/add',
        'view_path' => 'marketing/manjian/edit',
        'methods' => 'post',
        'sort' => '0',
        'status' => '1',
        'is_show' => '0',
    ],
];