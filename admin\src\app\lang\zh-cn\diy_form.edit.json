{"templatePagePlaceholder": "选择模板", "templatePageEmpty": "无", "changeTemplatePageTips": "切换模板后，当前页面内容将被替换且不被保存，请谨慎操作", "developTitle": "开发环境配置", "wapDomain": "wap域名（WAP_DOMAIN）", "wapDomainPlaceholder": "请输入wap域名", "pageSet": "页面设置", "tabEditContent": "内容", "tabEditStyle": "样式", "pageStyle": "页面样式", "pageContent": "页面内容", "statusBarContent": "导航栏内容", "statusBarStyle": "导航栏样式", "statusBarSwitchTips": "此处控制当前页面导航栏是否显示", "bottomNavContent": "底部导航内容", "diyPageTitle": "页面名称", "diyPageTitlePlaceholder": "请输入页面名称", "pageTitleTips": "页面名称用于后台显示", "diyTitle": "页面标题", "diyTitlePlaceholder": "请输入页面标题", "titleTips": "页面标题用于前台显示", "pageBgColor": "页面颜色", "bgUrl": "背景图片", "bgHeightScale": "高度比例", "bgHeightScaleTip": "为0时背景高度自适应展示", "marginSet": "边距设置", "componentStyleTitle": "组件样式", "bottomBgColor": "底部背景", "bottomBgTips": "底部背景包含边距和圆角", "componentBgColor": "组件背景色", "componentBgUrl": "组件背景图", "componentBgAlpha": "透明度", "bgGradientAngle": "渐变角度", "topToBottom": "从上到下", "leftToRight": "从左到右", "marginTop": "上边距", "marginBottom": "下边距", "marginBoth": "左右边距", "topRounded": "上圆角", "bottomRounded": "下圆角", "warmPrompt": "温馨提示", "leavePageTitleTips": "确定离开此页面？", "leavePageContentTips": "系统可能不会保存您所做的更改。", "decorating": "正在装修", "preview": "保存并预览", "moveUpComponent": "上移", "moveDownComponent": "下移", "copyComponent": "复制", "delComponent": "删除", "resetComponent": "重置", "tabbar": "底部导航", "tabbarSwitchTips": "此处控制当前页面底部导航菜单是否显示", "link": "链接地址", "delComponentTips": "确认要删除当前组件吗？", "notCopy": "无法复制", "componentCanOnlyAdd": "组件只能添加", "piece": "个", "componentNotMoved": "该组件禁止移动", "resetComponentTips": "确认要重置组件默认数据吗？", "image": "图片上传", "imageUpload": "图片上传", "imageSet": "图片设置", "imageAdsTips": "建议上传尺寸相同的图片，推荐尺寸750*350", "imageAdsSameScreenTips": "开启沉浸式样式，请确保该图片广告组件在页面中位于最顶端；为保证体验，请不要开导航栏；沉浸式样式仅在微信小程序中生效。", "sameScreen": "沉浸式", "addImageAd": "添加图片", "imageUrlTip": "请上传图片", "imageHeight": "图片高度", "imageHeightPlaceholder": "请输入图片高度", "imageHeightRegNum": "图片高度格式错误，请输入数字", "dataSources": "数据来源", "defaultSources": "默认", "manualSelectionSources": "手动选择", "selectPlaceholder": "请选择", "selected": "已选", "graphicNavModeTitle": "导航模式", "layoutMode": "排版模式", "layoutModeHorizontal": "横排", "layoutModeVertical": "竖排", "graphicNavSelectMode": "选择模式", "graphicNavModeGraphic": "图文导航", "graphicNavModeImg": "图片导航", "graphicNavModeText": "文字导航", "graphicNavImageSet": "图片设置", "graphicNavImageSize": "图片大小", "graphicNavAroundRadius": "图片圆角", "graphicNavShowStyle": "展示风格", "graphicNavStyleFixed": "固定显示", "graphicNavStyleSingleSlide": "单行滑动", "graphicNavStyleMultiLine": "多行滑动", "graphicNavStylePageSlide": "分页滑动", "graphicNavRowCount": "每行数量", "graphicNavPageCount": "显示方式", "graphicNavSetLabel": "导航设置", "singleLine": "单行", "multiline": "多行", "graphicNavTips": "建议上传尺寸相同的图片，推荐尺寸60*60", "graphicNavTitle": "标题", "graphicNavTitlePlaceholder": "请输入标题", "subGraphicNavTitle": "副标题", "subGraphicNavTitlePlaceholder": "请输入副标题", "subGraphicNavTitleLink": "副标题链接", "addGraphicNav": "添加导航", "blankHeightSet": "高度设置", "blankHeight": "空白高度", "styleSet": "风格设置", "titleStyle": "标题样式", "selectStyle": "风格选择", "activeCubeBlockBtnText": "按钮文字", "btnTextItalics": "斜体", "btnTextNormal": "常规", "styleLabel": "风格", "styleShowTips": "风格 1 2 3，仅在小程序中展示", "titleContent": "标题内容", "title": "标题名称", "titlePlaceholder": "请输入标题", "textAlign": "对齐方式", "textAlignLeft": "居左", "textAlignCenter": "居中", "textAlignRight": "居右", "textSet": "文字设置", "textFontSize": "文字大小", "textFontWeight": "文字粗细", "fontWeightBold": "加粗", "fontWeightNormal": "常规", "textColor": "文字颜色", "subTitleStyle": "副标题样式", "subTextBgColor": "背景色", "subTitleContent": "标题内容", "subTitle": "副标题", "subTitlePlaceholder": "请输入副标题", "moreContent": "“更多”按钮内容", "more": "文字", "morePlaceholder": "请输入文字", "moreIsShow": "是否显示", "memberStyle": "会员样式", "template": "模板", "imageGap": "图片间隙", "rubikCubeStyle": "魔方样式", "rubikCubeLayout": "魔方布局", "hotArea": "热区", "hotAreaSet": "热区设置", "hotAreaBackground": "热区背景", "addHotArea": "添加热区", "clickSet": "点击设置", "selectedAfterHotArea": "个热区", "hotAreaManage": "热区管理", "selectedHotArea": "请选择热区", "hotAreaLink": "的链接地址", "addonListSet": "应用设置", "addonListTips": "应用选择", "selectAddonTips": "请选择应用", "addonTitle": "应用名称", "addonDesc": "应用描述", "addonIcon": "应用图标", "selectAddon": "选择应用", "addAddon": "添加应用", "show": "显示", "hidden": "隐藏", "goodsCategoryTitle": "商品分类", "customGoods": "手动选择", "goodsNum": "商品数量", "selectCategory": "选择分类", "categoryName": "分类名称", "categoryImage": "分类图片", "selectSource": "选择数据源", "richTextContentSet": "内容设置", "richTextPlaceholder": "请输入富文本内容", "activeCubeBlockContent": "板块内容", "activeCubeTitle": "标题", "activeCubeTitlePlaceholder": "请输入标题", "activeCubeSubTitle": "副标题", "activeCubeSubTitlePlaceholder": "请输入副标题", "activeCubeButton": "按钮", "activeCubeButtonPlaceholder": "请输入按钮文字", "activeCubeButtonColor": "按钮颜色", "activeListFrameColor": "框体颜色", "activeCubeSubTitleTextColor": "文字颜色", "activeCubeSubTitleBgColor": "背景颜色", "activeCubeAddItem": "添加一个板块", "activeCubeBlockStyle": "板块样式", "activeCubeBlockTextFontWeight": "标题粗细", "noticeStyle": "公告风格", "noticeType": "类型", "noticeTypeImg": "图片", "noticeTypeText": "文字", "noticeTypeTextPlaceholder": "请输入公告标题", "noticeTitle": "公告标题", "addNotice": "添加公告", "noticeText": "公告内容", "noticeScrollWay": "滚动方式", "noticeUpDown": "上下滚动", "noticeHorizontal": "横向滚动", "noticeShowType": "点击类型", "noticeShowPopUp": "弹出公告内容", "noticeShowLink": "跳转链接", "dragMouseAdjustOrder": "鼠标拖拽可调整顺序", "noticePlaceholderText": "请输入公告内容", "carouselSearchShowPosition": "显示设置", "carouselSearchOpen": "开启", "carouselSearchClose": "关闭", "carouselSearchBgGradient": "背景渐变", "carouselSearchShowWay": "展示方式", "carouselSearchShowWayStatic": "正常显示", "carouselSearchShowWayFixed": "滚动至顶部固定", "carouselSearchFixedBgColor": "置顶背景", "carouselSearchStyleSelect": "风格选择", "carouselSearchSet": "搜索设置", "carouselSearchSubTitle": "副标题", "carouselSearchSubTitleStyle": "副标题样式", "carouselSearchPositionStyle": "定位样式", "carouselSearchSubTitlePlaceholder": "请输入副标题内容", "carouselSearchText": "搜索内容", "carouselSearchTextColor": "文字颜色", "carouselSearchBgColor": "背景颜色", "carouselSearchBtnColor": "按钮颜色", "carouselSearchBtnBgColor": "按钮背景色", "carouselSearchHotWordSet": "搜索热词", "carouselSearchHotWordInterval": "显示时间 / 秒", "carouselSearchHotWordText": "内容", "carouselSearchHotWordTextPlaceholder": "请输入热词", "carouselSearchAddHotWordItem": "添加一个热词", "carouselSearchLogoTips": "建议尺寸，70px * 30px", "carouselSearchTextTips": "搜索内容是默认展示数据，当添加搜索热词时，搜索内容隐藏; 当没有搜索热词时，搜索内容展示", "carouselSearchPlaceholder": "请输入搜索内容", "carouselSearchTabSet": "选项卡设置", "carouselSearchTabControl": "展示开关", "carouselSearchTabCategoryText": "分类名称", "carouselSearchTabCategoryTextPlaceholder": "请输入分类名称", "carouselSearchAddTabItem": "添加一个选项卡", "selectSourcesDiyPage": "选择微页面", "selectDiyPagePlaceholder": "请选择微页面", "diyPageTypeName": "页面类型", "diyPageForAddon": "所属应用", "carouselSearchSwiperSet": "轮播图设置", "carouselSearchSwiperControl": "展示开关", "carouselSearchSwiperInterval": "切换间隔 / 秒", "carouselSearchSwiperTips": "建议上传尺寸相同的图片，推荐尺寸750*350；鼠标拖拽可调整图片顺序", "carouselSearchTabStyle": "选项卡样式", "carouselSearchStyle": "搜索框样式", "noColor": "常规颜色", "selectColor": "选中颜色", "fixedNoColor": "下滑常规颜色", "fixedSelectColor": "下滑选中颜色", "carouselSearchSwiperIndicatorSet": "指示器设置", "carouselSearchSwiperIndicatorStyle": "指示器样式", "carouselSearchSwiperStyle": "轮播样式", "carouselSearchSwiperIndicatorStyle1": "样式1", "carouselSearchSwiperIndicatorStyle2": "样式2", "carouselSearchSwiperIndicatorStyle3": "样式3", "carouselSearchSwiperIndicatorAlign": "显示位置", "alignLeft": "居左", "alignCenter": "居中", "alignRight": "居右", "horzLineStyle": "线条风格", "horzLineStyleSolid": "实线", "horzLineStyleDashed": "虚线", "horzLineBorderColor": "线条颜色", "horzLineBorderWidth": "线条宽度", "floatBtnButton": "按钮位置", "floatBtnOffset": "上下偏移", "lateralBtnOffset": "左右偏移", "floatBtnImageSet": "图片设置", "floatBtnImageSize": "图片大小", "floatBtnAroundRadius": "图片圆角", "floatBtnImageSuggest": "建议上传正方形图片", "topStatusBarImg": "图片", "topStatusBarNav": "导航栏", "topStatusBarNavTips": "此处控制当前页面导航栏是否显示", "topStatusBarImgTips": "宽度自适应（最大150px），高度28px", "topStatusBarTextColor": "标题颜色", "topStatusBarBgColor": "头部颜色", "rollTopStatusBarBgColor": "滚动后头部颜色", "rollTopStatusBarTextColor": "滚动后标题颜色", "topStatusBarSearchName": "搜索内容", "topStatusBarSearchNamePlaceholder": "请输入搜索关键词", "settingTips": "点击查看如何配置", "pictureShowBlockOne": "模块一", "pictureShowBlockTwo": "模块二", "subTitleTextColor": "标题颜色", "pictureShowBgColor": "背景颜色", "pictureShowBtnText": "按钮文字", "pictureShowBtnColor": "文字颜色", "pictureShowBtnBgColor": "背景颜色", "pictureShowBlockStyle": "模块样式", "fieldNamePlaceholder": "请输入字段名称", "fieldRemarkPlaceholder": "请输入字段说明", "defaultValue": "默认值", "defaultValuePlaceholder": "请输入默认值", "formPlaceholder": "提示语", "formPlaceholderTips": "请输入提示语", "isRequired": "是否必填", "optionPlaceholder": "请输入选项内容", "formLayout": "表单布局", "layoutStyle": "排版风格", "singleTiling": "单列平铺‌", "singleTilingTipsOne": "将所有需要填写的表单内容项直接罗列在页面上。", "singleTilingTipsTwo": "适用于表单内容项较少且项目之间无逻辑关系的情况。", "singleTilingTipsThree": "其优势在于相对简洁、便于操作", "singleTilingTipsFour": "但当表单项数量较大时，一次性展示全部信息会增加用户的操作负担，填写效率较低", "arrangeSideBySide": "左右排列‌", "arrangeSideBySideTipsOne": "将表单分为左右两部分，左侧为标题和描述，右侧为输入区域。", "arrangeSideBySideTipsTwo": "这种布局适用于标题和描述内容较少的情况，能够提高表单的紧凑性和用户体验。", "layoutStyleTips": "切换后将同步所有表单组件的展示形式", "borderControl": "边框开关", "fieleContent": "字段内容", "fieldName": "字段名称", "filedRemark": "字段说明", "otherSetting": "其他设置", "hideControl": "隐藏该组件", "hideControlTipsOne": "勾选后填表人填表时看不到该字段。", "hideControlTipsTwo": "适用于你不再收集该字段又不希望删除已收集的数据。", "textStyle": "文字样式", "filedRemarkStyle": "字段说明样式", "style": "样式", "listStyle": "列表", "dropDownStyle": "下拉", "option": "选项", "addSingleOption": "添加单个选项", "addMultipleOption": "批量添加选项", "addOptionTips": "每个选项之间用英文“,” 隔开，自动过滤重复内容", "errorTipsOne": "存在重复选项，请检查内容", "errorTipsTwo": "选项已存在，请重新输入", "dataFormat": "日期格式", "startDate": "开始日期", "startTime": "开始时间", "startDataTips": "开始日期不能为空", "startTimeTips": "开始时间不能为空", "startDataPlaceholder": "请选择开始日期", "dataPlaceholder": "请选择日期", "timePlaceholder": "请选择时间", "startTimePlaceholder": "请选择开始时间", "endDate": "结束日期", "endTime": "结束时间", "endDataPlaceholder": "请选择结束日期", "endTimePlaceholder": "请选择结束时间", "endDataTips": "结束日期不能为空", "endTimeTips": "结束时间不能为空", "startEndDataTips": "开始日期不能大于结束日期", "startEndTimeTips": "开始时间不能大于结束时间", "currentDate": "当天日期", "diyDate": "指定日期", "currentTime": "当天时间", "diyTime": "指定时间", "preventDuplication": "内容防重复", "preventDuplicationTipsOne": "该组件填写的内容不能与已提交的数据重复。", "preventDuplicationTipsTwo": "极端情况下可能存在延时导致限制失效。", "privacyProtection": "隐私保护", "privacyProtectionTipsOne": "会自动将提交的个人信息做加密展示。", "privacyProtectionTipsTwo": "适用于公开展示收集的数据且不暴露用户隐私。", "privacyProtectionTipsThree": "提交后自动隐藏中间11位数字，仅管理员可查看", "privacyProtectionTipsFour": "提交后自动隐藏文本，仅管理员可查看", "privacyProtectionTipsFive": "提交后自动隐藏中间5位数字，仅管理员可查看", "imageLimit": "限制数量", "imageLimitPlaceholder": "请输入限制数量", "imageLimitErrorTips": "限制数量格式输入错误", "imageLimitErrorTipsTwo": "限制数量不能小于0", "imageLimitErrorTipsThree": "限制数量必须大于0", "imageLimitErrorTipsFour": "限制数量最大不能超过9", "defaultValueTips": "设置后，默认值会自动填充到输入框，填表人可在此基础上进行修改。", "defaultErrorTips": "默认值格式输入错误", "defaultMustZeroTips": "默认值不能小于0", "access": "获取方式", "authorizeWeChatLocation": "授权微信定位", "manuallySelectPositioning": "手动选择定位", "unit": "单位", "unitPlaceholder": "请输入单位", "followContent": "跟随内容", "hoverScreenBottom": "悬浮屏幕底部", "btnTips": "当表单内容多时，只有滚动页面至最底部才会显示提交按钮", "btnTipsTwo": "当表单内容多时，滚动页面至最底部时，提交按钮会自动按钮悬浮在屏幕底部，方便填表人快速提交显示在屏幕底部", "btnTipsThree": "若前端以嵌入形式调用表单，提交按钮组件将不显示，相关业务由该页面负责处理", "submitBtn": "提交按钮", "submitBtnName": "按钮名称", "btnNamePlaceholder": "请输入按钮名称", "submitBtnNamePlaceholder": "请输入提交按钮名称", "resetBtn": "重置按钮", "btnStyle": "按钮样式", "resetBtnNamePlaceholder": "请输入重置按钮名称", "rowCount": "显示行数", "rowCountPlaceholder": "请输入显示行数"}