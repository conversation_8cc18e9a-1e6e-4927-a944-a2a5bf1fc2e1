<?php

namespace addon\yz_she;

use app\service\admin\diy\DiyService;
use addon\yz_she\app\listener\ThemeColorListener;

/**
 * 插件安装之后单独的插件方法
 */
class Addon
{
    /**
     * 插件安装执行
     */
    public function install()
    {
        // 初始化主题色数据
        $this->initThemeColorData();

        return true;
    }

    /**
     * 插件卸载执行
     */
    public function uninstall()
    {
        // 删除主题色数据
        $this->deleteThemeColorData();

        return true;
    }

    /**
     * 插件升级执行
     */
    public function upgrade()
    {
        // 升级时重新初始化主题色数据
        $this->initThemeColorData();

        return true;
    }

    /**
     * 初始化主题色数据
     */
    private function initThemeColorData()
    {
        try {
            $listener = new ThemeColorListener();
            $addon_theme = $listener->handle(['key' => 'yz_she']);

            if (!empty($addon_theme)) {
                $diy_service = new DiyService();
                $diy_service->initAddonThemeColorData('yz_she', $addon_theme, 'addon');
            }
        } catch (\Exception $e) {
            // 记录错误日志，但不影响安装过程
            error_log('YZ_SHE Theme Color Init Error: ' . $e->getMessage());
        }
    }

    /**
     * 删除主题色数据
     */
    private function deleteThemeColorData()
    {
        try {
            // 删除该插件的主题色数据
            (new \app\model\diy\DiyTheme())->where([
                ['addon', '=', 'yz_she']
            ])->delete();
        } catch (\Exception $e) {
            // 记录错误日志，但不影响卸载过程
            error_log('YZ_SHE Theme Color Delete Error: ' . $e->getMessage());
        }
    }

}
