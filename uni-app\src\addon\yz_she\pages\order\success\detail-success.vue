<template>
  <view class="recycle-success-page">
      <!-- 商品信息卡片 -->
    <view class="product-card">
      <view class="product-info">
        <image
          :src="productInfo.image || '/static/images/default-product.png'"
          class="product-image"
          mode="aspectFit"
          @error="onImageError"
        ></image>
        <view class="product-details">
          <text class="product-name">{{ productInfo.name }}</text>
          <text class="product-code">{{ productInfo.code }}</text>
        </view>
        <image 
          src="/static/images/gucci-logo.png" 
          class="brand-logo" 
          mode="aspectFit"
        ></image>
      </view>
      
      <view class="price-section">
        <view class="price-info">
          <text class="price-label">预估回收</text>
          <view class="price-amount">
            <text class="currency">¥</text>
            <text class="price-value">{{ productInfo.price }}</text>
          </view>
        </view>
        
        <view class="price-breakdown">
          <text class="breakdown-item">评估金额: ¥{{ productInfo.evaluatePrice }}</text>
          <text class="breakdown-item">加价券: +¥{{ productInfo.bonus }}</text>
        </view>
      </view>
    </view>

    <!-- 成功状态区域 -->
    <view class="success-section">
      <view class="success-icon">
        <view class="clipboard-bg">
          <view class="clipboard-body">
            <view class="clipboard-lines">
              <view class="line"></view>
              <view class="line"></view>
              <view class="line"></view>
            </view>
          </view>
          <view class="clipboard-clip"></view>
        </view>
        <view class="check-mark">
          <u-icon name="checkmark" color="#fff" size="16"></u-icon>
        </view>
      </view>
      
      <text class="success-title">订单创建成功</text>
      <text class="order-number">订单号: {{ orderInfo.orderNo }}</text>
      <text class="success-desc">您可以在我的"我的订单"中查看该订单信息</text>

      <!-- 查看订单按钮 -->
      <view class="view-order-button" @click="viewOrder">
        <text class="button-text">查看订单</text>
      </view>
    </view>

  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getRecycleOrderDetail } from '@/addon/yz_she/api/recycle_order'
import { img } from '@/utils/common'

// 页面参数
const pageParams = ref<any>({})

// 商品信息
const productInfo = ref({
  name: '',
  code: '',
  image: '',
  price: '0.00',
  evaluatePrice: '0.00',
  bonus: '0'
})

// 订单信息
const orderInfo = ref({
  orderNo: '',
  status: '',
  createTime: ''
})

// 回收订单信息
const recycleOrderInfo = ref<any>({})

// 页面加载时获取参数
onLoad((options) => {
  console.log('成功页面接收到的参数:', options)
  pageParams.value = options || {}

  if (options.recycleOrderId) {
    loadOrderDetails(parseInt(options.recycleOrderId))
  } else {
    // 如果没有订单ID，显示错误信息
    uni.showToast({
      title: '订单信息缺失',
      icon: 'none'
    })
  }
})

// 加载订单详情
const loadOrderDetails = async (recycleOrderId: number) => {
  try {
    // 加载回收订单详情
    console.log('加载回收订单详情:', recycleOrderId)
    const recycleResponse = await getRecycleOrderDetail(recycleOrderId)

    if (recycleResponse.code === 1) {
      recycleOrderInfo.value = recycleResponse.data
      console.log('回收订单详情:', recycleResponse.data)

      // 设置订单基本信息
      orderInfo.value = {
        orderNo: recycleResponse.data.order_no || '',
        status: recycleResponse.data.status_text || '',
        createTime: recycleResponse.data.create_time_text || ''
      }

      // 设置商品信息
      productInfo.value = {
        name: recycleResponse.data.product_name || '',
        code: recycleResponse.data.product_code || '',
        image: recycleResponse.data.product_image ? img(recycleResponse.data.product_image) : '',
        price: (recycleResponse.data.expected_price + (recycleResponse.data.voucher_amount || 0)).toFixed(2),
        evaluatePrice: recycleResponse.data.expected_price?.toFixed(2) || '0.00',
        bonus: recycleResponse.data.voucher_amount?.toFixed(2) || '0'
      }
    } else {
      throw new Error(recycleResponse.msg || '获取回收订单详情失败')
    }

  } catch (error) {
    console.error('加载订单详情失败:', error)
    uni.showToast({
      title: error.message || '加载订单信息失败',
      icon: 'none'
    })
  }
}

// 查看订单
const viewOrder = () => {
  if (recycleOrderInfo.value.id) {
    uni.navigateTo({
      url: `/addon/yz_she/pages/order/order-list`
    })
  } else {
    uni.showToast({
      title: '订单信息缺失',
      icon: 'none'
    })
  }
}
</script>

<style lang="scss" scoped>
.recycle-success-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fffe 0%, #f0f9f8 50%, #e8f5f3 100%);
  position: relative;
}





// 商品信息卡片
.product-card {
  background-color: #fff;
  margin: 20rpx 32rpx;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(22, 160, 133, 0.12);
  border: 1rpx solid rgba(22, 160, 133, 0.1);

  .product-info {
    display: flex;
    align-items: center;
    gap: 20rpx;
    margin-bottom: 24rpx;

    .product-image {
      width: 120rpx;
      height: 120rpx;
      border-radius: 12rpx;
      background-color: #f8f9fa;
    }

    .product-details {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8rpx;

      .product-name {
        font-size: 26rpx;
        color: #333;
        font-weight: 500;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .product-code {
        font-size: 22rpx;
        color: #999;
      }
    }

    .brand-logo {
      width: 60rpx;
      height: 60rpx;
    }
  }

  .price-section {
    border-top: 1rpx solid rgba(22, 160, 133, 0.2);
    padding-top: 24rpx;

    .price-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12rpx;

      .price-label {
        font-size: 24rpx;
        color: #666;
      }

      .price-amount {
        display: flex;
        align-items: baseline;
        gap: 4rpx;

        .currency {
          font-size: 24rpx;
          color: #16a085;
          font-weight: 600;
        }

        .price-value {
          font-size: 36rpx;
          color: #16a085;
          font-weight: 700;
        }
      }
    }

    .price-breakdown {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .breakdown-item {
        font-size: 22rpx;
        color: #999;
      }
    }
  }
}

// 成功状态区域
.success-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 32rpx;
  text-align: center;

  .success-icon {
    position: relative;
    margin-bottom: 32rpx;

    .clipboard-bg {
      position: relative;
      width: 120rpx;
      height: 140rpx;

      .clipboard-body {
        width: 100%;
        height: 120rpx;
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        border-radius: 12rpx;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 8rpx 24rpx rgba(44, 62, 80, 0.3);

        .clipboard-lines {
          display: flex;
          flex-direction: column;
          gap: 8rpx;

          .line {
            width: 60rpx;
            height: 4rpx;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 2rpx;

            &:nth-child(2) {
              width: 48rpx;
            }

            &:nth-child(3) {
              width: 36rpx;
            }
          }
        }
      }

      .clipboard-clip {
        position: absolute;
        top: -8rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 40rpx;
        height: 16rpx;
        background-color: #fff;
        border-radius: 4rpx;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      }
    }

    .check-mark {
      position: absolute;
      bottom: -8rpx;
      right: -8rpx;
      width: 40rpx;
      height: 40rpx;
      background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4rpx 12rpx rgba(13, 115, 119, 0.5);
      border: 3rpx solid #fff;
    }
  }

  .success-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
  }

  .order-number {
    font-size: 26rpx;
    color: #16a085;
    font-weight: 600;
    margin-bottom: 16rpx;
  }

  .success-desc {
    font-size: 24rpx;
    color: #999;
    line-height: 1.5;
    max-width: 500rpx;
    margin-bottom: 32rpx;
  }

  // 查看订单按钮
  .view-order-button {
    padding: 16rpx 40rpx;
    background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
    border: none;
    border-radius: 32rpx;
    color: #fff;
    font-size: 28rpx;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 6rpx 20rpx rgba(13, 115, 119, 0.4);

    &:active {
      transform: scale(0.98);
      background: linear-gradient(135deg, #0a5d61 0%, #117a65 100%);
      box-shadow: 0 4rpx 16rpx rgba(13, 115, 119, 0.5);
    }

    .button-text {
      letter-spacing: 1rpx;
    }
  }
}





// 响应式适配
@media screen and (max-width: 375px) {
  .product-card {
    margin: 16rpx 24rpx;
    padding: 24rpx 20rpx;
  }

  .success-section {
    padding: 40rpx 24rpx;
  }

  .collect-button {
    right: 24rpx;
    padding: 20rpx 10rpx;

    .collect-text {
      font-size: 22rpx;
      letter-spacing: 3rpx;
    }
  }
}
</style>
