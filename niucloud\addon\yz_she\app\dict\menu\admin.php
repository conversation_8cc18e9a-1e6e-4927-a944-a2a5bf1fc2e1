<?php
return [
    [
        'menu_name' => '柚子奢侈品回收',
        'menu_key' => 'yz_she',
        'menu_short_name' => '柚子奢侈品回收',
        'parent_select_key' => '',
        'parent_key' => '',
        'menu_type' => '0',
        'icon' => '',
        'api_url' => '',
        'router_path' => '',
        'view_path' => '',
        'methods' => '',
        'sort' => '100',
        'status' => '1',
        'is_show' => '1',
        'children' => [
            [
                'menu_name' => '基础设置',
                'menu_key' => 'yz_she_basic_settings',
                'menu_short_name' => '基础设置',
                'parent_select_key' => '',
                'menu_type' => '0',
                'icon' => 'iconfont-iconshezhi',
                'api_url' => '',
                'router_path' => '',
                'view_path' => '',
                'methods' => '',
                'sort' => '100',
                'status' => '1',
                'is_show' => '1',
                'children' => [
                    [
                        'menu_name' => '回收设置',
                        'menu_key' => 'yz_she_system_config',
                        'menu_short_name' => '回收设置',
                        'parent_select_key' => '',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'yz_she/system_config',
                        'router_path' => 'yz_she/system_config',
                        'view_path' => 'system_config/system_config',
                        'methods' => 'get',
                        'sort' => '100',
                        'status' => '1',
                        'is_show' => '1',
                    ],
                    [
                        'menu_name' => '回收标准',
                        'menu_key' => 'yz_she_recycle_standard_list',
                        'menu_short_name' => '回收标准',
                        'parent_select_key' => '',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_standard',
                        'router_path' => 'yz_she/recycle_standard/list',
                        'view_path' => 'recycle_standard/list',
                        'methods' => 'get',
                        'sort' => '90',
                        'status' => '1',
                        'is_show' => '1',
                    ],
                    [
                        'menu_name' => '分类配置',
                        'menu_key' => 'yz_she_category_config_list',
                        'menu_short_name' => '分类配置',
                        'parent_select_key' => '',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'yz_she/category/config',
                        'router_path' => 'yz_she/category/config',
                        'view_path' => 'category/config',
                        'methods' => 'get',
                        'sort' => '80',
                        'status' => '1',
                        'is_show' => '1',
                    ],
                    [
                        'menu_name' => '地址管理',
                        'menu_key' => 'yz_she_member_address_list',
                        'menu_short_name' => '地址管理',
                        'parent_select_key' => '',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'yz_she/member/address',
                        'router_path' => 'yz_she/member/address_list',
                        'view_path' => 'member/address_list',
                        'methods' => 'get',
                        'sort' => '70',
                        'status' => '1',
                        'is_show' => '1',
                    ],
                    [
                        'menu_name' => '加价券管理',
                        'menu_key' => 'yz_she_voucher_list',
                        'menu_short_name' => '加价券管理',
                        'parent_select_key' => '',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'yz_she/voucher',
                        'router_path' => 'yz_she/voucher/list',
                        'view_path' => 'voucher/list',
                        'methods' => 'get',
                        'sort' => '60',
                        'status' => '1',
                        'is_show' => '1',
                    ],
                ],
            ],
            [
                'menu_name' => '商品管理',
                'menu_key' => 'yz_she_goods',
                'menu_short_name' => '商品管理',
                'parent_select_key' => '',
                'menu_type' => '0',
                'icon' => 'iconfont-iconshangpinliebiao',
                'api_url' => '',
                'router_path' => '',
                'view_path' => '',
                'methods' => '',
                'sort' => '90',
                'status' => '1',
                'is_show' => '1',
                'children' => [
                    [
                        'menu_name' => '商品分类',
                        'menu_key' => 'yz_she_goods_category',
                        'menu_short_name' => '商品分类',
                        'parent_select_key' => '',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'yz_she/goods/category',
                        'router_path' => 'yz_she/goods/category',
                        'view_path' => 'goods/category_list',
                        'methods' => 'get',
                        'sort' => '100',
                        'status' => '1',
                        'is_show' => '1',
                    ],
                    [
                        'menu_name' => '商品品牌',
                        'menu_key' => 'yz_she_goods_brand',
                        'menu_short_name' => '商品品牌',
                        'parent_select_key' => '',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'yz_she/goods/brand',
                        'router_path' => 'yz_she/goods/brand',
                        'view_path' => 'goods/brand_list',
                        'methods' => 'get',
                        'sort' => '90',
                        'status' => '1',
                        'is_show' => '1',
                    ],
                    [
                        'menu_name' => '商品列表',
                        'menu_key' => 'yz_she_goods_list',
                        'menu_short_name' => '商品列表',
                        'parent_select_key' => '',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'yz_she/goods',
                        'router_path' => 'yz_she/goods/list',
                        'view_path' => 'goods/goods_list',
                        'methods' => 'get',
                        'sort' => '80',
                        'status' => '1',
                        'is_show' => '1',
                    ],
                ],
            ],
            [
                'menu_name' => '订单管理',
                'menu_key' => 'yz_she_order_management',
                'menu_short_name' => '订单管理',
                'parent_select_key' => '',
                'menu_type' => '0',
                'icon' => 'iconfont-icondingdan',
                'api_url' => '',
                'router_path' => '',
                'view_path' => '',
                'methods' => '',
                'sort' => '80',
                'status' => '1',
                'is_show' => '1',
                'children' => [
                    [
                        'menu_name' => '估价订单',
                        'menu_key' => 'yz_she_quote_order_list',
                        'menu_short_name' => '估价订单',
                        'parent_select_key' => '',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'yz_she/quote_order/lists',
                        'router_path' => 'yz_she/quote_order',
                        'view_path' => 'quote_order/list',
                        'methods' => 'get',
                        'sort' => '100',
                        'status' => '1',
                        'is_show' => '1',
                    ],
                    [
                        'menu_name' => '回收订单',
                        'menu_key' => 'yz_she_recycle_order_list',
                        'menu_short_name' => '回收订单',
                        'parent_select_key' => '',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_order/lists',
                        'router_path' => 'yz_she/recycle_order',
                        'view_path' => 'recycle_order/list',
                        'methods' => 'get',
                        'sort' => '90',
                        'status' => '1',
                        'is_show' => '1',
                    ],
                    [
                        'menu_name' => '物流管理',
                        'menu_key' => 'yz_she_express_log_list',
                        'menu_short_name' => '物流管理',
                        'parent_select_key' => '',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'yz_she/express_log/lists',
                        'router_path' => 'yz_she/express_log',
                        'view_path' => 'express_log/list',
                        'methods' => 'get',
                        'sort' => '80',
                        'status' => '1',
                        'is_show' => '1',
                    ],
                ],
            ],
            [
                'menu_name' => 'API权限',
                'menu_key' => 'yz_she_api_permissions',
                'menu_short_name' => 'API权限',
                'parent_select_key' => '',
                'menu_type' => '0',
                'icon' => '',
                'api_url' => '',
                'router_path' => '',
                'view_path' => '',
                'methods' => '',
                'sort' => '10',
                'status' => '1',
                'is_show' => '0',
                'children' => [
                    [
                        'menu_name' => '添加回收标准',
                        'menu_key' => 'yz_she_recycle_standard_add',
                        'menu_short_name' => '添加回收标准',
                        'parent_select_key' => '',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_standard',
                        'router_path' => 'yz_she/recycle_standard/add',
                        'view_path' => 'recycle_standard/add',
                        'methods' => 'post',
                        'sort' => '100',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '编辑回收标准',
                        'menu_key' => 'yz_she_recycle_standard_edit',
                        'menu_short_name' => '编辑回收标准',
                        'parent_select_key' => '',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_standard/:id',
                        'router_path' => 'yz_she/recycle_standard/edit/:id',
                        'view_path' => 'recycle_standard/edit',
                        'methods' => 'put',
                        'sort' => '99',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '删除回收标准',
                        'menu_key' => 'yz_she_recycle_standard_del',
                        'menu_short_name' => '删除回收标准',
                        'parent_select_key' => '',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_standard/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'delete',
                        'sort' => '98',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '获取分类选项',
                        'menu_key' => 'yz_she_category_options',
                        'menu_short_name' => '获取分类选项',
                        'parent_select_key' => '',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'yz_she/category/options',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => '97',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '添加加价券',
                        'menu_key' => 'yz_she_voucher_add',
                        'menu_short_name' => '添加加价券',
                        'parent_select_key' => '',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'yz_she/voucher',
                        'router_path' => 'yz_she/voucher/add',
                        'view_path' => 'voucher/add',
                        'methods' => 'post',
                        'sort' => '96',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '编辑加价券',
                        'menu_key' => 'yz_she_voucher_edit',
                        'menu_short_name' => '编辑加价券',
                        'parent_select_key' => '',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'yz_she/voucher/:id',
                        'router_path' => 'yz_she/voucher/edit/:id',
                        'view_path' => 'voucher/edit',
                        'methods' => 'put',
                        'sort' => '95',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '估价订单详情',
                        'menu_key' => 'yz_she_quote_order_info',
                        'menu_short_name' => '估价订单详情',
                        'parent_select_key' => '',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'yz_she/quote_order/info/:id',
                        'router_path' => 'yz_she/quote_order/detail/:id',
                        'view_path' => 'quote_order/detail',
                        'methods' => 'get',
                        'sort' => '94',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '估价操作',
                        'menu_key' => 'yz_she_quote_order_quote',
                        'menu_short_name' => '估价操作',
                        'parent_select_key' => '',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'yz_she/quote_order/quote/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'post',
                        'sort' => '93',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '回收订单详情',
                        'menu_key' => 'yz_she_recycle_order_info',
                        'menu_short_name' => '回收订单详情',
                        'parent_select_key' => '',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_order/info/:id',
                        'router_path' => 'yz_she/recycle_order/detail/:id',
                        'view_path' => 'recycle_order/detail',
                        'methods' => 'get',
                        'sort' => '92',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '收货确认',
                        'menu_key' => 'yz_she_recycle_order_receive',
                        'menu_short_name' => '收货确认',
                        'parent_select_key' => '',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_order/receive/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => '91',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                    [
                        'menu_name' => '确认退回订单',
                        'menu_key' => 'yz_she_recycle_order_confirm_return',
                        'menu_short_name' => '确认退回订单',
                        'parent_select_key' => '',
                        'menu_type' => '1',
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_order/confirm_return/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'post',
                        'sort' => '90',
                        'status' => '1',
                        'is_show' => '0',
                    ],
                ],
            ],
        ],
    ],
];