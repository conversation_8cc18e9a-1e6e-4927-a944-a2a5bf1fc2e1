
-- ----------------------------
-- Table structure for {{prefix}}yz_she_brands
-- ----------------------------
DROP TABLE IF EXISTS `{{prefix}}yz_she_brands`;
CREATE TABLE `{{prefix}}yz_she_brands`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '品牌ID',
  `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '品牌名称',
  `logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '品牌Logo',
  `letter` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'A' COMMENT '首字母索引',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '是否热门品牌 1是 0否',
  `hot_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '热门品牌名字',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1启用 0禁用',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_letter`(`letter`) USING BTREE,
  INDEX `idx_is_hot`(`is_hot`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '品牌表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for {{prefix}}yz_she_categories
-- ----------------------------
DROP TABLE IF EXISTS `{{prefix}}yz_she_categories`;
CREATE TABLE `{{prefix}}yz_she_categories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类图标',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类图片',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1启用 0禁用',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for {{prefix}}yz_she_category_accessories
-- ----------------------------
DROP TABLE IF EXISTS `{{prefix}}yz_she_category_accessories`;
CREATE TABLE `{{prefix}}yz_she_category_accessories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `accessory_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配件名称',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分类配件配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for {{prefix}}yz_she_category_photos
-- ----------------------------
DROP TABLE IF EXISTS `{{prefix}}yz_she_category_photos`;
CREATE TABLE `{{prefix}}yz_she_category_photos`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `photo_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片名称',
  `background_image` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'upload/background/default-photo-bg.png' COMMENT '背景图片',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分类照片配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for {{prefix}}yz_she_express_log
-- ----------------------------
DROP TABLE IF EXISTS `{{prefix}}yz_she_express_log`;
CREATE TABLE `{{prefix}}yz_she_express_log`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint(20) UNSIGNED NOT NULL COMMENT '订单ID',
  `waybill` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运单号',
  `shopbill` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家单号',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '运单状态描述',
  `type_code` tinyint(1) NULL DEFAULT NULL COMMENT '状态码：1待揽收，2运输中，3已签收，4拒收退回，99已取消',
  `weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '下单重量',
  `real_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '站点称重',
  `transfer_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '分拣称重',
  `cal_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '计费重量',
  `volume` decimal(10, 2) NULL DEFAULT NULL COMMENT '体积',
  `parse_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '体积换算重量',
  `total_freight` decimal(10, 2) NULL DEFAULT NULL COMMENT '运单总扣款费用',
  `freight` decimal(10, 2) NULL DEFAULT NULL COMMENT '快递费',
  `freight_insured` decimal(10, 2) NULL DEFAULT NULL COMMENT '保价费',
  `freight_haocai` decimal(10, 2) NULL DEFAULT NULL COMMENT '增值费用',
  `change_bill` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '换单号',
  `change_bill_freight` decimal(10, 2) NULL DEFAULT NULL COMMENT '逆向费',
  `fee_over` tinyint(1) NULL DEFAULT NULL COMMENT '订单扣费状态：1已扣费，0冻结',
  `courier_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递员姓名',
  `courier_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递员电话',
  `pickup_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '取件码',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '回调原始内容',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_waybill`(`waybill`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '物流回调日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for {{prefix}}yz_she_goods
-- ----------------------------
DROP TABLE IF EXISTS `{{prefix}}yz_she_goods`;
CREATE TABLE `{{prefix}}yz_she_goods`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `brand_id` int(11) NOT NULL DEFAULT 0 COMMENT '品牌ID',
  `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品货号/型号',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品图片JSON格式',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品描述',
  `price_new` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '全新品回收价格',
  `price_used` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '二手品回收价格',
  `price_damaged` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '有瑕疵回收价格',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '是否热门商品 1是 0否',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1上架 0下架',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_brand_id`(`brand_id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_code`(`code`) USING BTREE,
  INDEX `idx_is_hot`(`is_hot`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品表' ROW_FORMAT = Dynamic;



-- ----------------------------
-- Table structure for {{prefix}}yz_she_quote_orders
-- ----------------------------
DROP TABLE IF EXISTS `{{prefix}}yz_she_quote_orders`;
CREATE TABLE `{{prefix}}yz_she_quote_orders`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `brand_id` int(11) NULL DEFAULT NULL COMMENT '品牌ID',
  `product_id` int(11) NULL DEFAULT NULL COMMENT '商品ID',
  `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `product_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品编码',
  `product_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品图片/品牌图片',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单状态:1=估价中,2=待确认,3=待发货,4=已完成,5=已取消',
  `quote_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '估价金额',
  `user_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户备注',
  `admin_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '管理员备注',
  `admin_id` int(11) NULL DEFAULT NULL COMMENT '估价管理员ID',
  `quote_time` datetime NULL DEFAULT NULL COMMENT '估价时间',
  `confirm_time` datetime NULL DEFAULT NULL COMMENT '用户确认时间',
  `ship_time` datetime NULL DEFAULT NULL COMMENT '用户发货时间',
  `complete_time` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `cancel_time` datetime NULL DEFAULT NULL COMMENT '取消时间',
  `cancel_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '取消原因',
  `auto_cancel_time` datetime NULL DEFAULT NULL COMMENT '自动取消时间(确认后48小时)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_no`(`order_no`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  INDEX `brand_id`(`brand_id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `admin_id`(`admin_id`) USING BTREE,
  INDEX `create_time`(`create_time`) USING BTREE,
  INDEX `auto_cancel_time`(`auto_cancel_time`) USING BTREE,
  INDEX `idx_orders_user_status_time`(`user_id`, `status`, `create_time`) USING BTREE,
  INDEX `idx_orders_status_time`(`status`, `create_time`) USING BTREE,
  INDEX `idx_orders_auto_cancel`(`status`, `auto_cancel_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 37 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价订单主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for {{prefix}}yz_she_order_status_logs
-- ----------------------------
DROP TABLE IF EXISTS `{{prefix}}yz_she_order_status_logs`;
CREATE TABLE `{{prefix}}yz_she_order_status_logs`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `from_status` tinyint(1) NULL DEFAULT NULL COMMENT '原状态',
  `to_status` tinyint(1) NOT NULL COMMENT '新状态',
  `operator_id` int(11) NULL DEFAULT NULL COMMENT '操作人ID',
  `operator_type` tinyint(1) NOT NULL COMMENT '操作人类型:1=用户,2=管理员,3=系统',
  `change_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更原因',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `operator_id`(`operator_id`) USING BTREE,
  INDEX `create_time`(`create_time`) USING BTREE,
  CONSTRAINT `fk_status_logs_order` FOREIGN KEY (`quote_order_id`) REFERENCES `{{prefix}}yz_she_quote_orders` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价订单状态变更日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for {{prefix}}yz_she_quote_accessories
-- ----------------------------
DROP TABLE IF EXISTS `{{prefix}}yz_she_quote_accessories`;
CREATE TABLE `{{prefix}}yz_she_quote_accessories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配件ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `accessory_config_id` int(11) NULL DEFAULT NULL COMMENT '配件配置ID',
  `accessory_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配件名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `accessory_config_id`(`accessory_config_id`) USING BTREE,
  INDEX `idx_accessories_order`(`quote_order_id`) USING BTREE,
  CONSTRAINT `fk_quote_accessories_order` FOREIGN KEY (`quote_order_id`) REFERENCES `{{prefix}}yz_she_quote_orders` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价订单配件表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for {{prefix}}yz_she_quote_photos
-- ----------------------------
DROP TABLE IF EXISTS `{{prefix}}yz_she_quote_photos`;
CREATE TABLE `{{prefix}}yz_she_quote_photos`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '照片ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `photo_config_id` int(11) NULL DEFAULT NULL COMMENT '照片配置ID',
  `photo_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片类型',
  `photo_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片名称',
  `photo_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片URL',
  `is_defect` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为瑕疵照片:0=否,1=是',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `photo_config_id`(`photo_config_id`) USING BTREE,
  INDEX `photo_type`(`photo_type`) USING BTREE,
  INDEX `is_defect`(`is_defect`) USING BTREE,
  INDEX `idx_photos_order_type`(`quote_order_id`, `photo_type`, `sort`) USING BTREE,
  CONSTRAINT `fk_quote_photos_order` FOREIGN KEY (`quote_order_id`) REFERENCES `{{prefix}}yz_she_quote_orders` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价订单照片表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for {{prefix}}yz_she_quote_records
-- ----------------------------
DROP TABLE IF EXISTS `{{prefix}}yz_she_quote_records`;
CREATE TABLE `{{prefix}}yz_she_quote_records`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `admin_id` int(11) NOT NULL COMMENT '估价管理员ID',
  `condition_score` int(3) NULL DEFAULT NULL COMMENT '成色评分(0-100)',
  `quote_price` decimal(10, 2) NOT NULL COMMENT '估价金额',
  `quote_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '估价说明',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `admin_id`(`admin_id`) USING BTREE,
  INDEX `create_time`(`create_time`) USING BTREE,
  INDEX `idx_records_order_time`(`quote_order_id`, `create_time`) USING BTREE,
  CONSTRAINT `fk_quote_records_order` FOREIGN KEY (`quote_order_id`) REFERENCES `{{prefix}}yz_she_quote_orders` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for {{prefix}}yz_she_recycle_order_logs
-- ----------------------------
DROP TABLE IF EXISTS `{{prefix}}yz_she_recycle_order_logs`;
CREATE TABLE `{{prefix}}yz_she_recycle_order_logs`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `recycle_order_id` int(11) NOT NULL COMMENT '回收订单ID',
  `from_status` tinyint(1) NULL DEFAULT NULL COMMENT '原状态',
  `to_status` tinyint(1) NOT NULL COMMENT '新状态',
  `operator_id` int(11) NULL DEFAULT NULL COMMENT '操作人ID',
  `operator_type` tinyint(1) NOT NULL COMMENT '操作人类型:1=用户,2=管理员,3=系统',
  `change_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更原因',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `extra_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '额外数据JSON',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_recycle_order_id`(`recycle_order_id`) USING BTREE,
  INDEX `idx_operator_id`(`operator_id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 315 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收订单状态变更日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for {{prefix}}yz_she_recycle_orders
-- ----------------------------
DROP TABLE IF EXISTS `{{prefix}}yz_she_recycle_orders`;
CREATE TABLE `{{prefix}}yz_she_recycle_orders`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '回收订单ID',
  `order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '回收订单编号',
  `quote_order_id` int(11) NULL DEFAULT NULL COMMENT '关联的估价订单ID（从估价订单创建时有值）',
  `member_id` int(11) NOT NULL COMMENT '会员ID（对应member表）',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `brand_id` int(11) NULL DEFAULT NULL COMMENT '品牌ID',
  `product_id` int(11) NULL DEFAULT NULL COMMENT '商品ID',
  `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `product_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品编码',
  `product_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品图片',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单状态:1=待取件,2=待收货,3=待质检,4=待确认,5=待退回,6=已退回,7=已完成,8=已取消',
  `source_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单来源:1=估价订单确认,2=直接回收,3=批量下单',
  `quantity` int(11) NOT NULL DEFAULT 1 COMMENT '回收数量',
  `expected_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '预期回收价格（用户选择的价格）',
  `final_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '最终回收价格（质检后确定）',
  `voucher_id` int(11) NULL DEFAULT NULL COMMENT '使用的加价券ID（对应{{prefix}}yz_she_voucher表）',
  `voucher_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '加价券金额',
  `total_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '最终结算金额',
  `delivery_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '配送方式:1=快递上门,2=自行寄出',
  `pickup_address_id` int(11) NULL DEFAULT NULL COMMENT '上门取件地址ID（对应{{prefix}}member_address表）',
  `pickup_time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '期望上门时间',
  `pickup_contact_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系人姓名',
  `pickup_contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系人电话',
  `pickup_address_detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '详细地址信息',
  `express_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递回调ID',
  `express_company` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递公司',
  `express_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递单号',
  `express_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '快递费用',
  `express_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '快递状态:0=暂无轨迹,1=已揽收,2=运输中,3=已签收,4=异常',
  `quality_check_admin_id` int(11) NULL DEFAULT NULL COMMENT '质检员ID',
  `quality_score` int(3) NULL DEFAULT NULL COMMENT '质检评分(0-100)',
  `quality_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '质检说明',
  `quality_images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '质检照片JSON',
  `settlement_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '结算方式:1=余额结算',
  `settlement_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '实际结算金额',
  `settlement_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '结算状态:0=未结算,1=已结算',
  `settlement_admin_id` int(11) NULL DEFAULT NULL COMMENT '结算操作员ID',
  `user_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户备注',
  `admin_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '管理员备注',
  `reject_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拒绝原因（用户不接受质检价格）',
  `return_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退回原因',
  `pickup_time_actual` int(11) NULL DEFAULT NULL COMMENT '实际取件时间（状态1→2）',
  `receive_time` int(11) NULL DEFAULT NULL COMMENT '平台收货时间（状态2→3）',
  `quality_start_time` int(11) NULL DEFAULT NULL COMMENT '质检开始时间（状态3→4）',
  `quality_complete_time` int(11) NULL DEFAULT NULL COMMENT '质检完成时间（状态4完成）',
  `confirm_time` int(11) NULL DEFAULT NULL COMMENT '用户确认时间（状态4→7或4→5）',
  `return_time` int(11) NULL DEFAULT NULL COMMENT '商品退回时间（状态5→6）',
  `return_express_company` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '退回快递公司',
  `return_express_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '退回快递单号',
  `return_note` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '退回备注',
  `settlement_time` int(11) NULL DEFAULT NULL COMMENT '结算完成时间（状态7）',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_no`(`order_no`) USING BTREE,
  INDEX `idx_quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `idx_member_id`(`member_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_source_type`(`source_type`) USING BTREE,
  INDEX `idx_delivery_type`(`delivery_type`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_member_status_time`(`member_id`, `status`, `create_time`) USING BTREE,
  INDEX `idx_pickup_address_id`(`pickup_address_id`) USING BTREE,
  INDEX `idx_voucher_id`(`voucher_id`) USING BTREE,
  INDEX `idx_express_status`(`express_status`) USING BTREE,
  INDEX `idx_return_express_no`(`return_express_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 76 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收订单主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for {{prefix}}yz_she_recycle_standards
-- ----------------------------
DROP TABLE IF EXISTS `{{prefix}}yz_she_recycle_standards`;
CREATE TABLE `{{prefix}}yz_she_recycle_standards`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '标准ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID（关联商品分类）',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标准项标题（如：外观、轻度磨损等）',
  `image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '示例图片',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '详细描述',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态：1启用 0禁用',
  `create_time` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_status_sort`(`category_id`, `status`, `sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收标准表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for {{prefix}}yz_she_system_config
-- ----------------------------
DROP TABLE IF EXISTS `{{prefix}}yz_she_system_config`;
CREATE TABLE `{{prefix}}yz_she_system_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `store_receiver_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货人姓名',
  `store_receiver_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货人电话',
  `store_province_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '省份名称',
  `store_city_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '城市名称',
  `store_district_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '区县名称',
  `store_address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '详细地址',
  `service_qr_code` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客服二维码',
  `service_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客服电话',
  `yunyang_appid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '云洋快递AppID',
  `yunyang_key` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '云洋快递Key',
  `yunyang_api_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '回调API地址',
  `yunyang_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否自动发单 0:禁用 1:启用',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '奢侈品系统配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for {{prefix}}yz_she_voucher
-- ----------------------------
DROP TABLE IF EXISTS `{{prefix}}yz_she_voucher`;
CREATE TABLE `{{prefix}}yz_she_voucher`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '加价券ID',
  `template_id` int(11) NOT NULL DEFAULT 0 COMMENT '模板ID',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `voucher_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '加价券编号',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '加价券标题',
  `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '类型：1-固定金额 2-百分比',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额或百分比',
  `min_condition_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最低使用金额',
  `max_discount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最大优惠金额',
  `applicable_goods_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '适用商品类型',
  `applicable_goods_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用商品ID',
  `applicable_category_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用分类ID',
  `applicable_brand_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用品牌ID',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-未使用 2-已使用 3-已过期',
  `receive_time` int(11) NOT NULL DEFAULT 0 COMMENT '发放时间',
  `use_time` int(11) NOT NULL DEFAULT 0 COMMENT '使用时间',
  `expire_time` int(11) NOT NULL DEFAULT 0 COMMENT '过期时间',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '使用的订单ID',
  `send_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '发放类型：1-注册自动发放 2-手动发放',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_voucher_no`(`voucher_no`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE,
  INDEX `idx_member_id`(`member_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_expire_time`(`expire_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 32 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户加价券表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for {{prefix}}yz_she_voucher_send_log
-- ----------------------------
DROP TABLE IF EXISTS `{{prefix}}yz_she_voucher_send_log`;
CREATE TABLE `{{prefix}}yz_she_voucher_send_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `template_id` int(11) NOT NULL DEFAULT 0 COMMENT '模板ID',
  `send_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '发放类型：1-注册自动发放 2-全部用户发放 3-指定用户发放',
  `send_count` int(11) NOT NULL DEFAULT 0 COMMENT '发放数量',
  `member_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '指定用户ID，JSON格式',
  `admin_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作管理员ID',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE,
  INDEX `idx_send_type`(`send_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '加价券发放记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for {{prefix}}yz_she_voucher_template
-- ----------------------------
DROP TABLE IF EXISTS `{{prefix}}yz_she_voucher_template`;
CREATE TABLE `{{prefix}}yz_she_voucher_template`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '加价券标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '加价券描述',
  `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '类型：1-固定金额 2-百分比',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额或百分比',
  `min_condition_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最低使用金额',
  `max_discount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最大优惠金额(百分比类型时使用)',
  `sum_count` int(11) NOT NULL DEFAULT -1 COMMENT '发放总数量，-1为不限制',
  `receive_count` int(11) NOT NULL DEFAULT 0 COMMENT '已发放数量',
  `use_count` int(11) NOT NULL DEFAULT 0 COMMENT '已使用数量',
  `valid_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '有效期类型：1-领取后N天有效 2-固定时间段',
  `length` int(11) NOT NULL DEFAULT 30 COMMENT '有效天数',
  `valid_start_time` int(11) NOT NULL DEFAULT 0 COMMENT '有效期开始时间',
  `valid_end_time` int(11) NOT NULL DEFAULT 0 COMMENT '有效期结束时间',
  `applicable_goods_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '适用商品类型：1-全部商品 2-指定商品 3-指定分类 4-指定品牌',
  `applicable_goods_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用商品ID，JSON格式',
  `applicable_category_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用分类ID，JSON格式',
  `applicable_brand_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用品牌ID，JSON格式',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：0-未开始 1-进行中 2-已结束 3-已关闭',
  `auto_send_register` tinyint(4) NOT NULL DEFAULT 0 COMMENT '新用户注册自动发放：0-否 1-是',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '加价券模板表' ROW_FORMAT = Dynamic;

