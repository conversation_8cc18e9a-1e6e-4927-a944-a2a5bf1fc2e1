[2025-08-05T20:18:39+08:00][sql] CONNECT:[ UseTime:0.000952s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:18:39+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001329s ]
[2025-08-05T20:18:39+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001045s ]
[2025-08-05T20:18:39+08:00][sql] SELECT `methods`,`api_url` FROM `www_sys_menu` WHERE (  `api_url` <> '' ) AND `www_sys_menu`.`delete_time` = '0' ORDER BY `sort` DESC [ RunTime:0.000561s ]
[2025-08-05T20:18:39+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.001580s ]
[2025-08-05T20:18:39+08:00][sql] SELECT `id`,`config_key`,`value`,`status`,`create_time`,`update_time` FROM `www_sys_config` WHERE  `config_key` = 'NIUCLOUD_CONFIG' LIMIT 1 [ RunTime:0.000208s ]
[2025-08-05T20:18:39+08:00][sql] SELECT `id`,`config_key`,`value`,`status`,`create_time`,`update_time` FROM `www_sys_config` WHERE  `config_key` = 'DEVELOPER_TOKEN' LIMIT 1 [ RunTime:0.000254s ]
[2025-08-05T20:18:39+08:00][error] DEBUG：>>>>>>>>>
服务主体：0
IP：127.0.0.1
耗时（毫秒）：413
请求类型：GET
应用：adminapi
路由：/index.php/adminapi/niucloud/authinfo
请求参数：[]
错误信息：{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":148,"message":"\u6388\u6743\u7801\u6216\u6388\u6743\u79d8\u94a5\u9519\u8bef","trace":[{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\guzzle\\src\\RetryMiddleware.php","line":84,"function":"core\\util\\niucloud\\{closure}","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\FulfilledPromise.php","line":41,"function":"GuzzleHttp\\{closure}","class":"GuzzleHttp\\RetryMiddleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php","line":48,"function":"GuzzleHttp\\Promise\\{closure}","class":"GuzzleHttp\\Promise\\FulfilledPromise","type":"::"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":248,"function":"run","class":"GuzzleHttp\\Promise\\TaskQueue","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":224,"function":"invokeWaitFn","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":269,"function":"waitIfPending","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":226,"function":"invokeWaitList","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":62,"function":"waitIfPending","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\guzzle\\src\\Client.php","line":187,"function":"wait","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\http\\HasHttpRequests.php","line":97,"function":"request","class":"GuzzleHttp\\Client","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":110,"function":"toRequest","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":193,"function":"request","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\service\\core\\niucloud\\CoreAuthService.php","line":31,"function":"httpGet","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\controller\\niucloud\\Module.php","line":35,"function":"getAuthInfo","class":"app\\service\\core\\niucloud\\CoreAuthService","type":"->"},{"function":"authorize","class":"app\\adminapi\\controller\\niucloud\\Module","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Container.php","line":345,"function":"invokeArgs","class":"ReflectionMethod","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":110,"function":"invokeReflectMethod","class":"think\\Container","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\route\\dispatch\\{closure}","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":113,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\Dispatch.php","line":52,"function":"exec","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":755,"function":"run","class":"think\\route\\Dispatch","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminLog.php","line":58,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminLog","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckRole.php","line":31,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckRole","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckToken.php","line":32,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckToken","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":756,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":208,"function":"dispatch","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":198,"function":"dispatchToRoute","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\app\\{closure}","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AllowCrossDomain.php","line":36,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AllowCrossDomain","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":72,"function":"then","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-trace\\src\\TraceDebug.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\trace\\TraceDebug","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":199,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":162,"function":"runWithRequest","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\public\\index.php","line":24,"function":"run","class":"think\\Http","type":"->"}],"previous":null}
---------
[2025-08-05T20:18:49+08:00][sql] CONNECT:[ UseTime:0.000881s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:18:49+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.001213s ]
[2025-08-05T20:18:49+08:00][sql] CONNECT:[ UseTime:0.000960s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:18:49+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.002100s ]
[2025-08-05T20:18:49+08:00][sql] CONNECT:[ UseTime:0.001172s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:18:49+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.004185s ]
[2025-08-05T20:18:49+08:00][sql] SELECT `id`,`config_key`,`value`,`status`,`create_time`,`update_time` FROM `www_sys_config` WHERE  `config_key` = 'WEB_SITE_INFO' LIMIT 1 [ RunTime:0.000571s ]
[2025-08-05T20:18:49+08:00][sql] CONNECT:[ UseTime:0.000954s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:18:49+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001339s ]
[2025-08-05T20:18:49+08:00][error] DEBUG：>>>>>>>>>
服务主体：0
IP：127.0.0.1
耗时（毫秒）：59
请求类型：GET
应用：adminapi
路由：/adminapi/auth/get
请求参数：[]
错误信息：{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\service\\admin\\auth\\LoginService.php","line":147,"message":"MUST_LOGIN","trace":[{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckToken.php","line":29,"function":"parseToken","class":"app\\service\\admin\\auth\\LoginService","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckToken","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":756,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":208,"function":"dispatch","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":198,"function":"dispatchToRoute","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\app\\{closure}","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AllowCrossDomain.php","line":36,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AllowCrossDomain","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":72,"function":"then","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-trace\\src\\TraceDebug.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\trace\\TraceDebug","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":199,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":162,"function":"runWithRequest","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\public\\index.php","line":24,"function":"run","class":"think\\Http","type":"->"}],"previous":null}
---------
[2025-08-05T20:18:50+08:00][sql] CONNECT:[ UseTime:0.010607s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:18:50+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.002124s ]
[2025-08-05T20:18:50+08:00][sql] CONNECT:[ UseTime:0.001205s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:18:50+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.001636s ]
[2025-08-05T20:18:50+08:00][sql] CONNECT:[ UseTime:0.001086s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:18:50+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.001983s ]
[2025-08-05T20:18:50+08:00][sql] CONNECT:[ UseTime:0.011245s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:18:50+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.002898s ]
[2025-08-05T20:18:50+08:00][sql] CONNECT:[ UseTime:0.010728s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:18:50+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.001773s ]
[2025-08-05T20:18:53+08:00][sql] CONNECT:[ UseTime:0.009900s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:18:53+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.002612s ]
[2025-08-05T20:18:53+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.001426s ]
[2025-08-05T20:18:53+08:00][sql] SELECT * FROM `www_sys_user` WHERE (  `username` = 'admin' ) AND `www_sys_user`.`delete_time` = '0' LIMIT 1 [ RunTime:0.000380s ]
[2025-08-05T20:18:53+08:00][sql] UPDATE `www_sys_user`  SET `last_time` = 1754396333 , `login_count` = 2 , `update_time` = 1754396333  WHERE (  `uid` = 1 ) AND `www_sys_user`.`delete_time` = '0' [ RunTime:0.000397s ]
[2025-08-05T20:18:53+08:00][sql] CONNECT:[ UseTime:0.000842s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:18:53+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000543s ]
[2025-08-05T20:18:53+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000199s ]
[2025-08-05T20:18:53+08:00][sql] CONNECT:[ UseTime:0.000942s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:18:53+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.002131s ]
[2025-08-05T20:18:53+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001879s ]
[2025-08-05T20:18:53+08:00][sql] SELECT `uid`,`username`,`head_img`,`real_name`,`last_ip`,`last_time`,`create_time`,`login_count`,`status`,`delete_time`,`update_time`,`role_ids`,`is_admin` FROM `www_sys_user` WHERE (  `uid` = 1 ) AND `www_sys_user`.`delete_time` = '0' LIMIT 1 [ RunTime:0.000455s ]
[2025-08-05T20:18:53+08:00][sql] SELECT * FROM `www_sys_menu` WHERE (  `addon` = ''  AND `status` = 1 ) AND `www_sys_menu`.`delete_time` = '0' ORDER BY `sort` DESC [ RunTime:0.002066s ]
[2025-08-05T20:18:54+08:00][sql] CONNECT:[ UseTime:0.000847s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:18:54+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000516s ]
[2025-08-05T20:18:54+08:00][sql] CONNECT:[ UseTime:0.000874s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:18:54+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.001641s ]
[2025-08-05T20:18:54+08:00][sql] CONNECT:[ UseTime:0.000986s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:18:54+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001339s ]
[2025-08-05T20:18:54+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.004504s ]
[2025-08-05T20:18:54+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000560s ]
[2025-08-05T20:18:54+08:00][sql] SELECT `id`,`config_key`,`value`,`status`,`create_time`,`update_time` FROM `www_sys_config` WHERE  `config_key` = 'START_UP_PAGE_DIY_INDEX' LIMIT 1 [ RunTime:0.000443s ]
[2025-08-05T20:18:54+08:00][sql] SHOW FULL COLUMNS FROM `www_diy_page` [ RunTime:0.002046s ]
[2025-08-05T20:18:54+08:00][sql] SELECT `id`,`title`,`page_title`,`name`,`template`,`type`,`mode`,`value`,`is_default`,`is_change`,`share`,`visit_count` FROM `www_diy_page` WHERE  `name` = 'DIY_INDEX'  AND `is_default` = 1 LIMIT 1 [ RunTime:0.001232s ]
[2025-08-05T20:18:54+08:00][sql] SHOW FULL COLUMNS FROM `www_diy_route` [ RunTime:0.002374s ]
[2025-08-05T20:18:54+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.001778s ]
[2025-08-05T20:18:54+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`cover` FROM `www_addon` WHERE  0 = 1  AND `status` = 1 [ RunTime:0.000280s ]
[2025-08-05T20:18:54+08:00][sql] CONNECT:[ UseTime:0.001745s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:18:54+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.002691s ]
[2025-08-05T20:18:54+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001678s ]
[2025-08-05T20:18:56+08:00][sql] CONNECT:[ UseTime:0.010988s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:18:56+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.002900s ]
[2025-08-05T20:18:56+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001878s ]
[2025-08-05T20:18:56+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.003020s ]
[2025-08-05T20:18:56+08:00][sql] SELECT `id`,`config_key`,`value`,`status`,`create_time`,`update_time` FROM `www_sys_config` WHERE  `config_key` = 'START_UP_PAGE_DIY_MEMBER_INDEX' LIMIT 1 [ RunTime:0.000381s ]
[2025-08-05T20:18:56+08:00][sql] SHOW FULL COLUMNS FROM `www_diy_page` [ RunTime:0.001337s ]
[2025-08-05T20:18:56+08:00][sql] SELECT `id`,`title`,`page_title`,`name`,`template`,`type`,`mode`,`value`,`is_default`,`is_change`,`share`,`visit_count` FROM `www_diy_page` WHERE  `name` = 'DIY_MEMBER_INDEX'  AND `is_default` = 1 LIMIT 1 [ RunTime:0.000342s ]
[2025-08-05T20:18:56+08:00][sql] SHOW FULL COLUMNS FROM `www_diy_route` [ RunTime:0.002366s ]
[2025-08-05T20:18:56+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000643s ]
[2025-08-05T20:18:56+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`cover` FROM `www_addon` WHERE  0 = 1  AND `status` = 1 [ RunTime:0.000353s ]
[2025-08-05T20:18:56+08:00][sql] CONNECT:[ UseTime:0.012132s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:18:56+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001763s ]
[2025-08-05T20:18:56+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001604s ]
[2025-08-05T20:18:56+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.002684s ]
[2025-08-05T20:19:00+08:00][sql] CONNECT:[ UseTime:0.000832s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:19:00+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001832s ]
[2025-08-05T20:19:00+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001621s ]
[2025-08-05T20:19:00+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000712s ]
[2025-08-05T20:19:00+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`author`,`version`,`install_time`,`update_time`,`cover`,`type` FROM `www_addon` [ RunTime:0.000325s ]
[2025-08-05T20:19:13+08:00][sql] CONNECT:[ UseTime:0.001380s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:19:13+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001650s ]
[2025-08-05T20:19:13+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000676s ]
[2025-08-05T20:19:13+08:00][sql] CONNECT:[ UseTime:0.000847s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:19:13+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000677s ]
[2025-08-05T20:19:13+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000664s ]
[2025-08-05T20:19:13+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000562s ]
[2025-08-05T20:19:13+08:00][error] DEBUG：>>>>>>>>>
服务主体：0
IP：127.0.0.1
耗时（毫秒）：231
请求类型：GET
应用：adminapi
路由：/adminapi/niucloud/authinfo
请求参数：[]
错误信息：{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":148,"message":"\u6388\u6743\u7801\u6216\u6388\u6743\u79d8\u94a5\u9519\u8bef","trace":[{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\guzzle\\src\\RetryMiddleware.php","line":84,"function":"core\\util\\niucloud\\{closure}","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\FulfilledPromise.php","line":41,"function":"GuzzleHttp\\{closure}","class":"GuzzleHttp\\RetryMiddleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php","line":48,"function":"GuzzleHttp\\Promise\\{closure}","class":"GuzzleHttp\\Promise\\FulfilledPromise","type":"::"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":248,"function":"run","class":"GuzzleHttp\\Promise\\TaskQueue","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":224,"function":"invokeWaitFn","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":269,"function":"waitIfPending","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":226,"function":"invokeWaitList","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":62,"function":"waitIfPending","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\guzzle\\src\\Client.php","line":187,"function":"wait","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\http\\HasHttpRequests.php","line":97,"function":"request","class":"GuzzleHttp\\Client","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":110,"function":"toRequest","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":193,"function":"request","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\service\\core\\niucloud\\CoreAuthService.php","line":31,"function":"httpGet","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\controller\\niucloud\\Module.php","line":35,"function":"getAuthInfo","class":"app\\service\\core\\niucloud\\CoreAuthService","type":"->"},{"function":"authorize","class":"app\\adminapi\\controller\\niucloud\\Module","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Container.php","line":345,"function":"invokeArgs","class":"ReflectionMethod","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":110,"function":"invokeReflectMethod","class":"think\\Container","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\route\\dispatch\\{closure}","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":113,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\Dispatch.php","line":52,"function":"exec","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":755,"function":"run","class":"think\\route\\Dispatch","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminLog.php","line":58,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminLog","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckRole.php","line":31,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckRole","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckToken.php","line":32,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckToken","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":756,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":208,"function":"dispatch","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":198,"function":"dispatchToRoute","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\app\\{closure}","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AllowCrossDomain.php","line":36,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AllowCrossDomain","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":72,"function":"then","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-trace\\src\\TraceDebug.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\trace\\TraceDebug","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":199,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":162,"function":"runWithRequest","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\public\\index.php","line":24,"function":"run","class":"think\\Http","type":"->"}],"previous":null}
---------
[2025-08-05T20:19:13+08:00][sql] CONNECT:[ UseTime:0.000884s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:19:13+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001639s ]
[2025-08-05T20:19:13+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001835s ]
[2025-08-05T20:19:13+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000883s ]
[2025-08-05T20:19:13+08:00][sql] CONNECT:[ UseTime:0.000842s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:19:13+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000586s ]
[2025-08-05T20:19:13+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000729s ]
[2025-08-05T20:19:15+08:00][sql] CONNECT:[ UseTime:0.000916s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:19:15+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000705s ]
[2025-08-05T20:19:15+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000596s ]
[2025-08-05T20:19:15+08:00][sql] SELECT `methods`,`api_url` FROM `www_sys_menu` WHERE (  `api_url` <> ''  AND `status` = 1 ) AND `www_sys_menu`.`delete_time` = '0' ORDER BY `sort` DESC [ RunTime:0.000511s ]
[2025-08-05T20:19:15+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.001274s ]
[2025-08-05T20:19:15+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`author`,`version`,`install_time`,`update_time`,`cover` FROM `www_addon` [ RunTime:0.000264s ]
[2025-08-05T20:19:15+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000757s ]
[2025-08-05T20:19:15+08:00][sql] CONNECT:[ UseTime:0.010601s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:19:15+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001219s ]
[2025-08-05T20:19:15+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001106s ]
[2025-08-05T20:19:15+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.001503s ]
[2025-08-05T20:19:16+08:00][sql] CONNECT:[ UseTime:0.001034s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:19:16+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001945s ]
[2025-08-05T20:19:16+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001316s ]
[2025-08-05T20:19:16+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000607s ]
[2025-08-05T20:19:16+08:00][sql] CONNECT:[ UseTime:0.011203s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:19:16+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001185s ]
[2025-08-05T20:19:16+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001557s ]
[2025-08-05T20:19:16+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000745s ]
[2025-08-05T20:19:17+08:00][sql] CONNECT:[ UseTime:0.009832s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:19:17+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000687s ]
[2025-08-05T20:19:17+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.002324s ]
[2025-08-05T20:19:17+08:00][sql] CONNECT:[ UseTime:0.001367s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:19:17+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000742s ]
[2025-08-05T20:19:17+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000678s ]
[2025-08-05T20:19:17+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000785s ]
[2025-08-05T20:19:17+08:00][error] DEBUG：>>>>>>>>>
服务主体：0
IP：127.0.0.1
耗时（毫秒）：238
请求类型：GET
应用：adminapi
路由：/adminapi/niucloud/authinfo
请求参数：[]
错误信息：{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":148,"message":"\u6388\u6743\u7801\u6216\u6388\u6743\u79d8\u94a5\u9519\u8bef","trace":[{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\guzzle\\src\\RetryMiddleware.php","line":84,"function":"core\\util\\niucloud\\{closure}","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\FulfilledPromise.php","line":41,"function":"GuzzleHttp\\{closure}","class":"GuzzleHttp\\RetryMiddleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php","line":48,"function":"GuzzleHttp\\Promise\\{closure}","class":"GuzzleHttp\\Promise\\FulfilledPromise","type":"::"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":248,"function":"run","class":"GuzzleHttp\\Promise\\TaskQueue","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":224,"function":"invokeWaitFn","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":269,"function":"waitIfPending","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":226,"function":"invokeWaitList","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":62,"function":"waitIfPending","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\guzzle\\src\\Client.php","line":187,"function":"wait","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\http\\HasHttpRequests.php","line":97,"function":"request","class":"GuzzleHttp\\Client","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":110,"function":"toRequest","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":193,"function":"request","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\service\\core\\niucloud\\CoreAuthService.php","line":31,"function":"httpGet","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\controller\\niucloud\\Module.php","line":35,"function":"getAuthInfo","class":"app\\service\\core\\niucloud\\CoreAuthService","type":"->"},{"function":"authorize","class":"app\\adminapi\\controller\\niucloud\\Module","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Container.php","line":345,"function":"invokeArgs","class":"ReflectionMethod","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":110,"function":"invokeReflectMethod","class":"think\\Container","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\route\\dispatch\\{closure}","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":113,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\Dispatch.php","line":52,"function":"exec","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":755,"function":"run","class":"think\\route\\Dispatch","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminLog.php","line":58,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminLog","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckRole.php","line":31,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckRole","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckToken.php","line":32,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckToken","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":756,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":208,"function":"dispatch","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":198,"function":"dispatchToRoute","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\app\\{closure}","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AllowCrossDomain.php","line":36,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AllowCrossDomain","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":72,"function":"then","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-trace\\src\\TraceDebug.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\trace\\TraceDebug","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":199,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":162,"function":"runWithRequest","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\public\\index.php","line":24,"function":"run","class":"think\\Http","type":"->"}],"previous":null}
---------
[2025-08-05T20:19:17+08:00][sql] CONNECT:[ UseTime:0.001054s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:19:17+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000648s ]
[2025-08-05T20:19:17+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000714s ]
[2025-08-05T20:19:17+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.001686s ]
[2025-08-05T20:19:19+08:00][sql] CONNECT:[ UseTime:0.010772s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:19:19+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001883s ]
[2025-08-05T20:19:19+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001477s ]
[2025-08-05T20:19:19+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user_log` [ RunTime:0.001426s ]
[2025-08-05T20:19:19+08:00][sql] INSERT INTO `www_sys_user_log` SET `uid` = 1 , `username` = 'admin' , `url` = '/adminapi/addon/install/yz_she' , `params` = '{\"addon\":\"yz_she\"}' , `ip` = '127.0.0.1' , `type` = 'POST' , `operation` = '插件管理-安装插件' , `create_time` = 1754396358 [ RunTime:0.000324s ]
[2025-08-05T20:19:19+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000674s ]
[2025-08-05T20:19:19+08:00][sql] SELECT * FROM `www_addon` WHERE  `key` = 'yz_she' LIMIT 1 [ RunTime:0.000623s ]
[2025-08-05T20:19:19+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000397s ]
[2025-08-05T20:19:19+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000407s ]
[2025-08-05T20:19:19+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000268s ]
[2025-08-05T20:19:19+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_brands` [ RunTime:0.000648s ]
[2025-08-05T20:19:19+08:00][sql] CREATE TABLE `www_yz_she_brands` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '品牌ID',
  `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '品牌名称',
  `logo` varchar(255) DEFAULT NULL COMMENT '品牌Logo',
  `letter` char(1) NOT NULL DEFAULT 'A' COMMENT '首字母索引',
  `is_hot` tinyint(1) DEFAULT 0 COMMENT '是否热门品牌 1是 0否',
  `hot_name` varchar(255) DEFAULT NULL COMMENT '热门品牌名字',
  `sort` int(11) DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1启用 0禁用',
  `create_time` int(11) DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_letter` (`letter`),
  KEY `idx_is_hot` (`is_hot`),
  KEY `idx_sort` (`sort`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='品牌表' [ RunTime:0.018578s ]
[2025-08-05T20:19:19+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_categories` [ RunTime:0.000487s ]
[2025-08-05T20:19:19+08:00][sql] CREATE TABLE `www_yz_she_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `icon` varchar(255) DEFAULT NULL COMMENT '分类图标',
  `image` varchar(255) DEFAULT NULL COMMENT '分类图片',
  `sort` int(11) DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1启用 0禁用',
  `create_time` int(11) DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sort` (`sort`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='回收分类表' [ RunTime:0.014143s ]
[2025-08-05T20:19:19+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_category_accessories` [ RunTime:0.000309s ]
[2025-08-05T20:19:19+08:00][sql] CREATE TABLE `www_yz_she_category_accessories` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `accessory_name` varchar(50) NOT NULL COMMENT '配件名称',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  KEY `sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='分类配件配置表' [ RunTime:0.013055s ]
[2025-08-05T20:19:19+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_category_photos` [ RunTime:0.000410s ]
[2025-08-05T20:19:19+08:00][sql] CREATE TABLE `www_yz_she_category_photos` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `photo_name` varchar(50) NOT NULL COMMENT '照片名称',
  `background_image` varchar(200) DEFAULT 'upload/background/default-photo-bg.png' COMMENT '背景图片',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  KEY `sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='分类照片配置表' [ RunTime:0.013657s ]
[2025-08-05T20:19:19+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_express_log` [ RunTime:0.000391s ]
[2025-08-05T20:19:19+08:00][sql] CREATE TABLE `www_yz_she_express_log` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint(20) UNSIGNED NOT NULL COMMENT '订单ID',
  `waybill` varchar(64) NOT NULL COMMENT '运单号',
  `shopbill` varchar(64) DEFAULT NULL COMMENT '商家单号',
  `type` varchar(50) DEFAULT NULL COMMENT '运单状态描述',
  `status` varchar(50) DEFAULT NULL COMMENT '运单状态',
  `message` text COMMENT '状态描述',
  `data` text COMMENT '回调数据',
  `create_time` int(11) DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_waybill` (`waybill`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='物流回调日志表' [ RunTime:0.013374s ]
[2025-08-05T20:19:19+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_goods` [ RunTime:0.000513s ]
[2025-08-05T20:19:19+08:00][sql] CREATE TABLE `www_yz_she_goods` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `brand_id` int(11) NOT NULL DEFAULT 0 COMMENT '品牌ID',
  `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  `name` varchar(255) NOT NULL COMMENT '商品名称',
  `code` varchar(100) DEFAULT NULL COMMENT '商品货号/型号',
  `image` varchar(500) DEFAULT NULL COMMENT '商品图片',
  `images` text COMMENT '商品图片集',
  `description` text COMMENT '商品描述',
  `price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '参考价格',
  `sort` int(11) DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1启用 0禁用',
  `create_time` int(11) DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_brand_id` (`brand_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_sort` (`sort`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品表' [ RunTime:0.021966s ]
[2025-08-05T20:19:19+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_order_status_logs` [ RunTime:0.000258s ]
[2025-08-05T20:19:19+08:00][sql] CREATE TABLE `www_yz_she_order_status_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `from_status` tinyint(1) DEFAULT NULL COMMENT '原状态',
  `to_status` tinyint(1) NOT NULL COMMENT '新状态',
  `operator_id` int(11) DEFAULT NULL COMMENT '操作人ID',
  `operator_type` varchar(20) DEFAULT 'admin' COMMENT '操作人类型',
  `remark` text COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `quote_order_id` (`quote_order_id`),
  KEY `operator_id` (`operator_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='估价订单状态变更日志表' [ RunTime:0.013432s ]
[2025-08-05T20:19:19+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_quote_accessories` [ RunTime:0.000325s ]
[2025-08-05T20:19:19+08:00][sql] CREATE TABLE `www_yz_she_quote_accessories` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配件ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `accessory_config_id` int(11) DEFAULT NULL COMMENT '配件配置ID',
  `accessory_name` varchar(100) NOT NULL COMMENT '配件名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `quote_order_id` (`quote_order_id`),
  KEY `accessory_config_id` (`accessory_config_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='估价订单配件表' [ RunTime:0.013592s ]
[2025-08-05T20:19:19+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_quote_orders` [ RunTime:0.000337s ]
[2025-08-05T20:19:19+08:00][sql] CREATE TABLE `www_yz_she_quote_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单编号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `brand_id` int(11) DEFAULT NULL COMMENT '品牌ID',
  `goods_id` int(11) DEFAULT NULL COMMENT '商品ID',
  `goods_name` varchar(255) DEFAULT NULL COMMENT '商品名称',
  `goods_code` varchar(100) DEFAULT NULL COMMENT '商品型号',
  `purchase_year` varchar(10) DEFAULT NULL COMMENT '购买年份',
  `purchase_price` decimal(10,2) DEFAULT NULL COMMENT '购买价格',
  `condition_description` text COMMENT '物品状况描述',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-待估价 2-已估价 3-已取消',
  `quote_price` decimal(10,2) DEFAULT NULL COMMENT '估价金额',
  `quote_time` datetime DEFAULT NULL COMMENT '估价时间',
  `quote_admin_id` int(11) DEFAULT NULL COMMENT '估价管理员ID',
  `quote_remark` text COMMENT '估价备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `user_id` (`user_id`),
  KEY `category_id` (`category_id`),
  KEY `brand_id` (`brand_id`),
  KEY `goods_id` (`goods_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='估价订单主表' [ RunTime:0.021078s ]
[2025-08-05T20:19:19+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_quote_photos` [ RunTime:0.000417s ]
[2025-08-05T20:19:19+08:00][sql] CREATE TABLE `www_yz_she_quote_photos` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '照片ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `photo_config_id` int(11) DEFAULT NULL COMMENT '照片配置ID',
  `photo_type` varchar(50) NOT NULL COMMENT '照片类型',
  `photo_name` varchar(100) NOT NULL COMMENT '照片名称',
  `photo_url` varchar(500) NOT NULL COMMENT '照片URL',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `quote_order_id` (`quote_order_id`),
  KEY `photo_config_id` (`photo_config_id`),
  KEY `photo_type` (`photo_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='估价订单照片表' [ RunTime:0.015426s ]
[2025-08-05T20:19:19+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_quote_records` [ RunTime:0.000394s ]
[2025-08-05T20:19:19+08:00][sql] CREATE TABLE `www_yz_she_quote_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `admin_id` int(11) NOT NULL COMMENT '估价管理员ID',
  `condition_score` int(3) DEFAULT NULL COMMENT '成色评分(0-100)',
  `quote_price` decimal(10,2) NOT NULL COMMENT '估价金额',
  `remark` text COMMENT '估价备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `quote_order_id` (`quote_order_id`),
  KEY `admin_id` (`admin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='估价记录表' [ RunTime:0.013652s ]
[2025-08-05T20:19:19+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_recycle_order_logs` [ RunTime:0.000396s ]
[2025-08-05T20:19:19+08:00][sql] CREATE TABLE `www_yz_she_recycle_order_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `recycle_order_id` int(11) NOT NULL COMMENT '回收订单ID',
  `from_status` tinyint(1) DEFAULT NULL COMMENT '原状态',
  `to_status` tinyint(1) NOT NULL COMMENT '新状态',
  `operator_id` int(11) DEFAULT NULL COMMENT '操作人ID',
  `operator_type` varchar(20) DEFAULT 'admin' COMMENT '操作人类型',
  `remark` text COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `recycle_order_id` (`recycle_order_id`),
  KEY `operator_id` (`operator_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='回收订单状态变更日志表' [ RunTime:0.013735s ]
[2025-08-05T20:19:19+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_recycle_orders` [ RunTime:0.000344s ]
[2025-08-05T20:19:19+08:00][sql] CREATE TABLE `www_yz_she_recycle_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '回收订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '回收订单编号',
  `quote_order_id` int(11) DEFAULT NULL COMMENT '关联的估价订单ID（从估价订单创建时有值）',
  `member_id` int(11) NOT NULL COMMENT '会员ID（对应member表）',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `brand_id` int(11) DEFAULT NULL COMMENT '品牌ID',
  `goods_id` int(11) DEFAULT NULL COMMENT '商品ID',
  `goods_name` varchar(255) DEFAULT NULL COMMENT '商品名称',
  `goods_code` varchar(100) DEFAULT NULL COMMENT '商品型号',
  `purchase_year` varchar(10) DEFAULT NULL COMMENT '购买年份',
  `purchase_price` decimal(10,2) DEFAULT NULL COMMENT '购买价格',
  `condition_description` text COMMENT '物品状况描述',
  `quote_price` decimal(10,2) DEFAULT NULL COMMENT '估价金额',
  `final_price` decimal(10,2) DEFAULT NULL COMMENT '最终回收价格',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-待寄送 2-已寄送 3-已收货 4-检测中 5-已完成 6-已取消 7-退回中 8-已退回',
  `express_company` varchar(50) DEFAULT NULL COMMENT '快递公司',
  `express_no` varchar(50) DEFAULT NULL COMMENT '快递单号',
  `send_time` datetime DEFAULT NULL COMMENT '寄送时间',
  `receive_time` datetime DEFAULT NULL COMMENT '收货时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
  `cancel_reason` varchar(255) DEFAULT NULL COMMENT '取消原因',
  `admin_remark` text COMMENT '管理员备注',
  `member_remark` text COMMENT '用户备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `quote_order_id` (`quote_order_id`),
  KEY `member_id` (`member_id`),
  KEY `category_id` (`category_id`),
  KEY `brand_id` (`brand_id`),
  KEY `goods_id` (`goods_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='回收订单主表' [ RunTime:0.020117s ]
[2025-08-05T20:19:19+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_recycle_standards` [ RunTime:0.000349s ]
[2025-08-05T20:19:19+08:00][sql] CREATE TABLE `www_yz_she_recycle_standards` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '标准ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID（关联商品分类）',
  `title` varchar(100) NOT NULL COMMENT '标准项标题（如：外观、轻度磨损等）',
  `image` varchar(500) DEFAULT NULL COMMENT '示例图片',
  `description` text COMMENT '详细描述',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='回收标准表' [ RunTime:0.012092s ]
[2025-08-05T20:19:19+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_system_config` [ RunTime:0.000491s ]
[2025-08-05T20:19:19+08:00][sql] CREATE TABLE `www_yz_she_system_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `store_receiver_name` varchar(50) NOT NULL DEFAULT '' COMMENT '收货人姓名',
  `store_receiver_phone` varchar(20) NOT NULL DEFAULT '' COMMENT '收货人电话',
  `store_province_name` varchar(50) NOT NULL DEFAULT '' COMMENT '省份名称',
  `store_city_name` varchar(50) NOT NULL DEFAULT '' COMMENT '城市名称',
  `store_district_name` varchar(50) NOT NULL DEFAULT '' COMMENT '区县名称',
  `store_address` varchar(255) NOT NULL DEFAULT '' COMMENT '详细地址',
  `store_full_address` varchar(500) NOT NULL DEFAULT '' COMMENT '完整地址',
  `create_time` int(11) DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='奢侈品系统配置表' [ RunTime:0.010604s ]
[2025-08-05T20:19:19+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_voucher` [ RunTime:0.000525s ]
[2025-08-05T20:19:19+08:00][sql] CREATE TABLE `www_yz_she_voucher` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '加价券ID',
  `template_id` int(11) NOT NULL DEFAULT 0 COMMENT '模板ID',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `voucher_no` varchar(32) NOT NULL DEFAULT '' COMMENT '加价券编号',
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT '加价券标题',
  `description` text COMMENT '加价券描述',
  `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '类型：1-固定金额 2-百分比',
  `price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额或百分比',
  `min_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '最低使用金额',
  `max_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '最高优惠金额（百分比类型时有效）',
  `start_time` int(11) NOT NULL DEFAULT 0 COMMENT '开始时间',
  `end_time` int(11) NOT NULL DEFAULT 0 COMMENT '结束时间',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-未使用 2-已使用 3-已过期',
  `use_time` int(11) NOT NULL DEFAULT 0 COMMENT '使用时间',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '使用的订单ID',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `voucher_no` (`voucher_no`),
  KEY `template_id` (`template_id`),
  KEY `member_id` (`member_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户加价券表' [ RunTime:0.016801s ]
[2025-08-05T20:19:19+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_voucher_send_log` [ RunTime:0.000419s ]
[2025-08-05T20:19:19+08:00][sql] CREATE TABLE `www_yz_she_voucher_send_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `template_id` int(11) NOT NULL DEFAULT 0 COMMENT '模板ID',
  `send_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '发放类型：1-注册自动发放 2-全部用户发放 3-指定用户发放',
  `send_count` int(11) NOT NULL DEFAULT 0 COMMENT '发放数量',
  `member_ids` text COMMENT '指定用户ID，JSON格式',
  `admin_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作管理员ID',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `template_id` (`template_id`),
  KEY `admin_id` (`admin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='加价券发放记录表' [ RunTime:0.013992s ]
[2025-08-05T20:19:19+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_voucher_template` [ RunTime:0.000321s ]
[2025-08-05T20:19:19+08:00][sql] CREATE TABLE `www_yz_she_voucher_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT '加价券标题',
  `description` text COMMENT '加价券描述',
  `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '类型：1-固定金额 2-百分比',
  `price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额或百分比',
  `min_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '最低使用金额',
  `max_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '最高优惠金额（百分比类型时有效）',
  `validity_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '有效期类型：1-固定时间 2-领取后N天',
  `start_time` int(11) NOT NULL DEFAULT 0 COMMENT '开始时间（固定时间类型）',
  `end_time` int(11) NOT NULL DEFAULT 0 COMMENT '结束时间（固定时间类型）',
  `validity_days` int(11) NOT NULL DEFAULT 0 COMMENT '有效天数（领取后N天类型）',
  `total_count` int(11) NOT NULL DEFAULT 0 COMMENT '发放总数量，0为不限制',
  `receive_count` int(11) NOT NULL DEFAULT 0 COMMENT '已领取数量',
  `per_limit` int(11) NOT NULL DEFAULT 1 COMMENT '每人限领数量',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-启用 0-禁用',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `status` (`status`),
  KEY `validity_type` (`validity_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='加价券模板表'; [ RunTime:0.013680s ]
[2025-08-05T20:19:19+08:00][sql] DELETE FROM `www_sys_menu` WHERE  `addon` = 'yz_she'  AND `source` = 'system' [ RunTime:0.000523s ]
[2025-08-05T20:19:19+08:00][sql] INSERT INTO `www_sys_menu` (`menu_name` , `menu_short_name` , `menu_key` , `app_type` , `addon` , `parent_key` , `menu_type` , `icon` , `api_url` , `router_path` , `view_path` , `methods` , `sort` , `status` , `is_show`) VALUES ( '柚子奢侈品回收','柚子奢侈品回收','yz_she','admin','yz_she','',0,'','','','','',100,1,1 ) , ( '基础设置','基础设置','yz_she_basic_settings','admin','yz_she','yz_she',0,'iconfont-iconshezhi','','','','',100,1,1 ) , ( '回收设置','回收设置','yz_she_system_config','admin','yz_she','yz_she_basic_settings',1,'','yz_she/system_config','yz_she/system_config','system_config/system_config','get',100,1,1 ) , ( '回收标准','回收标准','yz_she_recycle_standard_list','admin','yz_she','yz_she_basic_settings',1,'','yz_she/recycle_standard','yz_she/recycle_standard/list','recycle_standard/list','get',90,1,1 ) , ( '分类配置','分类配置','yz_she_category_config_list','admin','yz_she','yz_she_basic_settings',1,'','yz_she/category/config','yz_she/category/config','category/config','get',80,1,1 ) , ( '地址管理','地址管理','yz_she_member_address_list','admin','yz_she','yz_she_basic_settings',1,'','yz_she/member/address','yz_she/member/address_list','member/address_list','get',70,1,1 ) , ( '加价券管理','加价券管理','yz_she_voucher_list','admin','yz_she','yz_she_basic_settings',1,'','yz_she/voucher','yz_she/voucher/list','voucher/list','get',60,1,1 ) , ( '商品管理','商品管理','yz_she_goods','admin','yz_she','yz_she',0,'iconfont-iconshangpinliebiao','','','','',90,1,1 ) , ( '商品分类','商品分类','yz_she_goods_category','admin','yz_she','yz_she_goods',1,'','yz_she/goods/category','yz_she/goods/category','goods/category_list','get',100,1,1 ) , ( '商品品牌','商品品牌','yz_she_goods_brand','admin','yz_she','yz_she_goods',1,'','yz_she/goods/brand','yz_she/goods/brand','goods/brand_list','get',90,1,1 ) , ( '商品列表','商品列表','yz_she_goods_list','admin','yz_she','yz_she_goods',1,'','yz_she/goods','yz_she/goods/list','goods/goods_list','get',80,1,1 ) , ( '订单管理','订单管理','yz_she_order_management','admin','yz_she','yz_she',0,'iconfont-icondingdan','','','','',80,1,1 ) , ( '估价订单','估价订单','yz_she_quote_order_list','admin','yz_she','yz_she_order_management',1,'','yz_she/quote_order/lists','yz_she/quote_order','quote_order/list','get',100,1,1 ) , ( '回收订单','回收订单','yz_she_recycle_order_list','admin','yz_she','yz_she_order_management',1,'','yz_she/recycle_order/lists','yz_she/recycle_order','recycle_order/list','get',90,1,1 ) , ( '物流管理','物流管理','yz_she_express_log_list','admin','yz_she','yz_she_order_management',1,'','yz_she/express_log/lists','yz_she/express_log','express_log/list','get',80,1,1 ) , ( 'API权限','API权限','yz_she_api_permissions','admin','yz_she','yz_she',0,'','','','','',10,1,0 ) , ( '添加回收标准','添加回收标准','yz_she_recycle_standard_add','admin','yz_she','yz_she_api_permissions',1,'','yz_she/recycle_standard','yz_she/recycle_standard/add','recycle_standard/add','post',100,1,0 ) , ( '编辑回收标准','编辑回收标准','yz_she_recycle_standard_edit','admin','yz_she','yz_she_api_permissions',1,'','yz_she/recycle_standard/:id','yz_she/recycle_standard/edit/:id','recycle_standard/edit','put',99,1,0 ) , ( '删除回收标准','删除回收标准','yz_she_recycle_standard_del','admin','yz_she','yz_she_api_permissions',1,'','yz_she/recycle_standard/:id','','','delete',98,1,0 ) , ( '获取分类选项','获取分类选项','yz_she_category_options','admin','yz_she','yz_she_api_permissions',1,'','yz_she/category/options','','','get',97,1,0 ) , ( '添加加价券','添加加价券','yz_she_voucher_add','admin','yz_she','yz_she_api_permissions',1,'','yz_she/voucher','yz_she/voucher/add','voucher/add','post',96,1,0 ) , ( '编辑加价券','编辑加价券','yz_she_voucher_edit','admin','yz_she','yz_she_api_permissions',1,'','yz_she/voucher/:id','yz_she/voucher/edit/:id','voucher/edit','put',95,1,0 ) , ( '估价订单详情','估价订单详情','yz_she_quote_order_info','admin','yz_she','yz_she_api_permissions',1,'','yz_she/quote_order/info/:id','yz_she/quote_order/detail/:id','quote_order/detail','get',94,1,0 ) , ( '估价操作','估价操作','yz_she_quote_order_quote','admin','yz_she','yz_she_api_permissions',1,'','yz_she/quote_order/quote/:id','','','post',93,1,0 ) , ( '回收订单详情','回收订单详情','yz_she_recycle_order_info','admin','yz_she','yz_she_api_permissions',1,'','yz_she/recycle_order/info/:id','yz_she/recycle_order/detail/:id','recycle_order/detail','get',92,1,0 ) , ( '收货确认','收货确认','yz_she_recycle_order_receive','admin','yz_she','yz_she_api_permissions',1,'','yz_she/recycle_order/receive/:id','','','put',91,1,0 ) , ( '确认退回订单','确认退回订单','yz_she_recycle_order_confirm_return','admin','yz_she','yz_she_api_permissions',1,'','yz_she/recycle_order/confirm_return/:id','','','post',90,1,0 ) [ RunTime:0.004448s ]
[2025-08-05T20:19:19+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_schedule` [ RunTime:0.001121s ]
[2025-08-05T20:19:19+08:00][sql] SELECT * FROM `www_addon` WHERE  `key` = 'yz_she' LIMIT 1 [ RunTime:0.000277s ]
[2025-08-05T20:19:19+08:00][sql] INSERT INTO `www_addon` SET `title` = '柚子奢侈品回收' , `version` = '1.0.0' , `status` = 1 , `desc` = '柚子奢侈品回收' , `icon` = 'addon/yz_she/icon.png' , `key` = 'yz_she' , `type` = 'app' , `support_app` = '' , `install_time` = 1754396359 , `create_time` = 1754396359 , `update_time` = 1754396359 [ RunTime:0.000238s ]
[2025-08-05T20:19:24+08:00][sql] CONNECT:[ UseTime:0.010745s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:19:24+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000645s ]
[2025-08-05T20:19:24+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000601s ]
[2025-08-05T20:19:24+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000573s ]
[2025-08-05T20:19:25+08:00][sql] CONNECT:[ UseTime:0.010663s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:19:25+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000641s ]
[2025-08-05T20:19:25+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001485s ]
[2025-08-05T20:19:25+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user_log` [ RunTime:0.002155s ]
[2025-08-05T20:19:25+08:00][sql] INSERT INTO `www_sys_user_log` SET `uid` = 1 , `username` = 'admin' , `url` = '/adminapi/addon/uninstall/yz_she' , `params` = '{\"addon\":\"yz_she\"}' , `ip` = '127.0.0.1' , `type` = 'POST' , `operation` = '插件管理-卸载插件' , `create_time` = 1754396364 [ RunTime:0.000345s ]
[2025-08-05T20:19:25+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000688s ]
[2025-08-05T20:19:25+08:00][sql] SELECT `menu_name`,`menu_key`,`menu_short_name`,`parent_select_key`,`parent_key`,`menu_type`,`icon`,`api_url`,`router_path`,`view_path`,`methods`,`sort`,`status`,`is_show` FROM `www_sys_menu` WHERE (  `app_type` = 'admin'  AND `addon` = 'yz_she' ) AND `www_sys_menu`.`delete_time` = '0' ORDER BY `sort` DESC [ RunTime:0.000756s ]
[2025-08-05T20:19:25+08:00][sql] UPDATE `www_sys_menu`  SET `source` = 'system'  WHERE (  `app_type` = 'admin'  AND `addon` = 'yz_she' ) AND `www_sys_menu`.`delete_time` = '0' [ RunTime:0.002424s ]
[2025-08-05T20:19:25+08:00][sql] SELECT * FROM `www_addon` WHERE  `key` = 'yz_she' LIMIT 1 [ RunTime:0.000551s ]
[2025-08-05T20:19:25+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_brands` [ RunTime:0.007462s ]
[2025-08-05T20:19:25+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_categories` [ RunTime:0.005734s ]
[2025-08-05T20:19:25+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_category_accessories` [ RunTime:0.006025s ]
[2025-08-05T20:19:25+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_category_photos` [ RunTime:0.005935s ]
[2025-08-05T20:19:25+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_express_log` [ RunTime:0.005562s ]
[2025-08-05T20:19:25+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_goods` [ RunTime:0.006335s ]
[2025-08-05T20:19:25+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_order_status_logs` [ RunTime:0.005525s ]
[2025-08-05T20:19:25+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_quote_accessories` [ RunTime:0.005603s ]
[2025-08-05T20:19:25+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_quote_orders` [ RunTime:0.005906s ]
[2025-08-05T20:19:25+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_quote_photos` [ RunTime:0.005892s ]
[2025-08-05T20:19:25+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_quote_records` [ RunTime:0.005629s ]
[2025-08-05T20:19:25+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_recycle_order_logs` [ RunTime:0.005977s ]
[2025-08-05T20:19:25+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_recycle_orders` [ RunTime:0.009157s ]
[2025-08-05T20:19:25+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_recycle_standards` [ RunTime:0.006634s ]
[2025-08-05T20:19:25+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_system_config` [ RunTime:0.005527s ]
[2025-08-05T20:19:25+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_voucher` [ RunTime:0.005695s ]
[2025-08-05T20:19:25+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_voucher_send_log` [ RunTime:0.005626s ]
[2025-08-05T20:19:25+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_voucher_template`; [ RunTime:0.005719s ]
[2025-08-05T20:19:25+08:00][sql] DELETE FROM `www_sys_menu` WHERE  `addon` = 'yz_she' [ RunTime:0.004227s ]
[2025-08-05T20:19:25+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_schedule` [ RunTime:0.016418s ]
[2025-08-05T20:19:25+08:00][sql] DELETE FROM `www_sys_schedule` WHERE  `addon` = 'yz_she' [ RunTime:0.000178s ]
[2025-08-05T20:19:25+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000387s ]
[2025-08-05T20:19:25+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000305s ]
[2025-08-05T20:19:25+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000311s ]
[2025-08-05T20:19:25+08:00][sql] DELETE FROM `www_addon` WHERE  `key` = 'yz_she' [ RunTime:0.001526s ]
[2025-08-05T20:20:17+08:00][sql] CONNECT:[ UseTime:0.001336s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:20:17+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.005514s ]
[2025-08-05T20:20:17+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000523s ]
[2025-08-05T20:20:17+08:00][sql] CONNECT:[ UseTime:0.000900s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:20:17+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001844s ]
[2025-08-05T20:20:17+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000654s ]
[2025-08-05T20:20:17+08:00][sql] SELECT * FROM `www_sys_menu` WHERE (  `addon` = ''  AND `status` = 1 ) AND `www_sys_menu`.`delete_time` = '0' ORDER BY `sort` DESC [ RunTime:0.001238s ]
[2025-08-05T20:20:18+08:00][sql] CONNECT:[ UseTime:0.011201s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:20:18+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000676s ]
[2025-08-05T20:20:18+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001935s ]
[2025-08-05T20:20:18+08:00][sql] CONNECT:[ UseTime:0.000874s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:20:18+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000582s ]
[2025-08-05T20:20:18+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000473s ]
[2025-08-05T20:20:18+08:00][sql] CONNECT:[ UseTime:0.012699s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:20:18+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.002270s ]
[2025-08-05T20:20:18+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001663s ]
[2025-08-05T20:20:18+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.004833s ]
[2025-08-05T20:20:18+08:00][sql] CONNECT:[ UseTime:0.000941s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:20:18+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000671s ]
[2025-08-05T20:20:18+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000584s ]
[2025-08-05T20:20:18+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000518s ]
[2025-08-05T20:20:18+08:00][sql] CONNECT:[ UseTime:0.000826s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:20:18+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000552s ]
[2025-08-05T20:20:18+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000538s ]
[2025-08-05T20:20:18+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000426s ]
[2025-08-05T20:20:18+08:00][error] DEBUG：>>>>>>>>>
服务主体：0
IP：127.0.0.1
耗时（毫秒）：260
请求类型：GET
应用：adminapi
路由：/adminapi/niucloud/authinfo
请求参数：[]
错误信息：{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":148,"message":"\u6388\u6743\u7801\u6216\u6388\u6743\u79d8\u94a5\u9519\u8bef","trace":[{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\guzzle\\src\\RetryMiddleware.php","line":84,"function":"core\\util\\niucloud\\{closure}","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\FulfilledPromise.php","line":41,"function":"GuzzleHttp\\{closure}","class":"GuzzleHttp\\RetryMiddleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php","line":48,"function":"GuzzleHttp\\Promise\\{closure}","class":"GuzzleHttp\\Promise\\FulfilledPromise","type":"::"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":248,"function":"run","class":"GuzzleHttp\\Promise\\TaskQueue","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":224,"function":"invokeWaitFn","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":269,"function":"waitIfPending","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":226,"function":"invokeWaitList","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":62,"function":"waitIfPending","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\guzzle\\src\\Client.php","line":187,"function":"wait","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\http\\HasHttpRequests.php","line":97,"function":"request","class":"GuzzleHttp\\Client","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":110,"function":"toRequest","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":193,"function":"request","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\service\\core\\niucloud\\CoreAuthService.php","line":31,"function":"httpGet","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\controller\\niucloud\\Module.php","line":35,"function":"getAuthInfo","class":"app\\service\\core\\niucloud\\CoreAuthService","type":"->"},{"function":"authorize","class":"app\\adminapi\\controller\\niucloud\\Module","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Container.php","line":345,"function":"invokeArgs","class":"ReflectionMethod","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":110,"function":"invokeReflectMethod","class":"think\\Container","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\route\\dispatch\\{closure}","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":113,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\Dispatch.php","line":52,"function":"exec","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":755,"function":"run","class":"think\\route\\Dispatch","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminLog.php","line":58,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminLog","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckRole.php","line":31,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckRole","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckToken.php","line":32,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckToken","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":756,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":208,"function":"dispatch","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":198,"function":"dispatchToRoute","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\app\\{closure}","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AllowCrossDomain.php","line":36,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AllowCrossDomain","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":72,"function":"then","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-trace\\src\\TraceDebug.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\trace\\TraceDebug","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":199,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":162,"function":"runWithRequest","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\public\\index.php","line":24,"function":"run","class":"think\\Http","type":"->"}],"previous":null}
---------
[2025-08-05T20:20:20+08:00][sql] CONNECT:[ UseTime:0.010494s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:20:20+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000709s ]
[2025-08-05T20:20:20+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000784s ]
[2025-08-05T20:20:20+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000674s ]
[2025-08-05T20:20:20+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`author`,`version`,`install_time`,`update_time`,`cover` FROM `www_addon` [ RunTime:0.000371s ]
[2025-08-05T20:20:20+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000798s ]
[2025-08-05T20:20:20+08:00][sql] CONNECT:[ UseTime:0.010964s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:20:20+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001074s ]
[2025-08-05T20:20:20+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001129s ]
[2025-08-05T20:20:21+08:00][sql] CONNECT:[ UseTime:0.010926s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:20:21+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.002453s ]
[2025-08-05T20:20:21+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.002117s ]
[2025-08-05T20:20:21+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.001457s ]
[2025-08-05T20:20:21+08:00][sql] CONNECT:[ UseTime:0.011125s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:20:21+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000642s ]
[2025-08-05T20:20:21+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000677s ]
[2025-08-05T20:20:21+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000574s ]
[2025-08-05T20:20:21+08:00][sql] CONNECT:[ UseTime:0.011072s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:20:21+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000691s ]
[2025-08-05T20:20:21+08:00][sql] CONNECT:[ UseTime:0.010070s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:20:21+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000684s ]
[2025-08-05T20:20:21+08:00][sql] CONNECT:[ UseTime:0.001394s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:20:21+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000691s ]
[2025-08-05T20:20:21+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000582s ]
[2025-08-05T20:20:21+08:00][sql] CONNECT:[ UseTime:0.010710s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:20:21+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000599s ]
[2025-08-05T20:20:21+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000736s ]
[2025-08-05T20:20:21+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000618s ]
[2025-08-05T20:20:21+08:00][error] DEBUG：>>>>>>>>>
服务主体：0
IP：127.0.0.1
耗时（毫秒）：249
请求类型：GET
应用：adminapi
路由：/adminapi/niucloud/authinfo
请求参数：[]
错误信息：{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":148,"message":"\u6388\u6743\u7801\u6216\u6388\u6743\u79d8\u94a5\u9519\u8bef","trace":[{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\guzzle\\src\\RetryMiddleware.php","line":84,"function":"core\\util\\niucloud\\{closure}","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\FulfilledPromise.php","line":41,"function":"GuzzleHttp\\{closure}","class":"GuzzleHttp\\RetryMiddleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php","line":48,"function":"GuzzleHttp\\Promise\\{closure}","class":"GuzzleHttp\\Promise\\FulfilledPromise","type":"::"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":248,"function":"run","class":"GuzzleHttp\\Promise\\TaskQueue","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":224,"function":"invokeWaitFn","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":269,"function":"waitIfPending","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":226,"function":"invokeWaitList","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":62,"function":"waitIfPending","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\guzzle\\src\\Client.php","line":187,"function":"wait","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\http\\HasHttpRequests.php","line":97,"function":"request","class":"GuzzleHttp\\Client","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":110,"function":"toRequest","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":193,"function":"request","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\service\\core\\niucloud\\CoreAuthService.php","line":31,"function":"httpGet","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\controller\\niucloud\\Module.php","line":35,"function":"getAuthInfo","class":"app\\service\\core\\niucloud\\CoreAuthService","type":"->"},{"function":"authorize","class":"app\\adminapi\\controller\\niucloud\\Module","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Container.php","line":345,"function":"invokeArgs","class":"ReflectionMethod","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":110,"function":"invokeReflectMethod","class":"think\\Container","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\route\\dispatch\\{closure}","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":113,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\Dispatch.php","line":52,"function":"exec","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":755,"function":"run","class":"think\\route\\Dispatch","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminLog.php","line":58,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminLog","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckRole.php","line":31,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckRole","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckToken.php","line":32,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckToken","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":756,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":208,"function":"dispatch","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":198,"function":"dispatchToRoute","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\app\\{closure}","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AllowCrossDomain.php","line":36,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AllowCrossDomain","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":72,"function":"then","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-trace\\src\\TraceDebug.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\trace\\TraceDebug","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":199,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":162,"function":"runWithRequest","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\public\\index.php","line":24,"function":"run","class":"think\\Http","type":"->"}],"previous":null}
---------
[2025-08-05T20:20:22+08:00][sql] CONNECT:[ UseTime:0.010152s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:20:22+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000831s ]
[2025-08-05T20:20:22+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000721s ]
[2025-08-05T20:20:22+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000683s ]
[2025-08-05T20:20:23+08:00][sql] CONNECT:[ UseTime:0.000931s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:20:23+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000923s ]
[2025-08-05T20:20:23+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001023s ]
[2025-08-05T20:20:23+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user_log` [ RunTime:0.001484s ]
[2025-08-05T20:20:23+08:00][sql] INSERT INTO `www_sys_user_log` SET `uid` = 1 , `username` = 'admin' , `url` = '/adminapi/addon/install/yz_she' , `params` = '{\"addon\":\"yz_she\"}' , `ip` = '127.0.0.1' , `type` = 'POST' , `operation` = '插件管理-安装插件' , `create_time` = 1754396423 [ RunTime:0.000274s ]
[2025-08-05T20:20:23+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000610s ]
[2025-08-05T20:20:23+08:00][sql] SELECT * FROM `www_addon` WHERE  `key` = 'yz_she' LIMIT 1 [ RunTime:0.000587s ]
[2025-08-05T20:20:23+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000345s ]
[2025-08-05T20:20:23+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000289s ]
[2025-08-05T20:20:23+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000426s ]
[2025-08-05T20:20:23+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_brands` [ RunTime:0.000234s ]
[2025-08-05T20:20:23+08:00][sql] CREATE TABLE `www_yz_she_brands`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '品牌ID',
  `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '品牌名称',
  `logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '品牌Logo',
  `letter` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'A' COMMENT '首字母索引',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '是否热门品牌 1是 0否',
  `hot_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '热门品牌名字',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1启用 0禁用',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_letter`(`letter`) USING BTREE,
  INDEX `idx_is_hot`(`is_hot`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '品牌表' ROW_FORMAT = Dynamic [ RunTime:0.025437s ]
[2025-08-05T20:20:23+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_categories` [ RunTime:0.000985s ]
[2025-08-05T20:20:23+08:00][sql] CREATE TABLE `www_yz_she_categories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类图标',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类图片',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1启用 0禁用',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收分类表' ROW_FORMAT = Dynamic [ RunTime:0.018457s ]
[2025-08-05T20:20:23+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_category_accessories` [ RunTime:0.000735s ]
[2025-08-05T20:20:23+08:00][sql] CREATE TABLE `www_yz_she_category_accessories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `accessory_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配件名称',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分类配件配置表' ROW_FORMAT = Dynamic [ RunTime:0.014686s ]
[2025-08-05T20:20:23+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_category_photos` [ RunTime:0.000355s ]
[2025-08-05T20:20:23+08:00][sql] CREATE TABLE `www_yz_she_category_photos`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `photo_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片名称',
  `background_image` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'upload/background/default-photo-bg.png' COMMENT '背景图片',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分类照片配置表' ROW_FORMAT = Dynamic [ RunTime:0.015475s ]
[2025-08-05T20:20:23+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_express_log` [ RunTime:0.001048s ]
[2025-08-05T20:20:23+08:00][sql] CREATE TABLE `www_yz_she_express_log`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint(20) UNSIGNED NOT NULL COMMENT '订单ID',
  `waybill` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运单号',
  `shopbill` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家单号',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '运单状态描述',
  `type_code` tinyint(1) NULL DEFAULT NULL COMMENT '状态码：1待揽收，2运输中，3已签收，4拒收退回，99已取消',
  `weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '下单重量',
  `real_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '站点称重',
  `transfer_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '分拣称重',
  `cal_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '计费重量',
  `volume` decimal(10, 2) NULL DEFAULT NULL COMMENT '体积',
  `parse_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '体积换算重量',
  `total_freight` decimal(10, 2) NULL DEFAULT NULL COMMENT '运单总扣款费用',
  `freight` decimal(10, 2) NULL DEFAULT NULL COMMENT '快递费',
  `freight_insured` decimal(10, 2) NULL DEFAULT NULL COMMENT '保价费',
  `freight_haocai` decimal(10, 2) NULL DEFAULT NULL COMMENT '增值费用',
  `change_bill` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '换单号',
  `change_bill_freight` decimal(10, 2) NULL DEFAULT NULL COMMENT '逆向费',
  `fee_over` tinyint(1) NULL DEFAULT NULL COMMENT '订单扣费状态：1已扣费，0冻结',
  `courier_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递员姓名',
  `courier_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递员电话',
  `pickup_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '取件码',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '回调原始内容',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_waybill`(`waybill`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '物流回调日志表' ROW_FORMAT = DYNAMIC [ RunTime:0.022066s ]
[2025-08-05T20:20:23+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_goods` [ RunTime:0.000653s ]
[2025-08-05T20:20:23+08:00][sql] CREATE TABLE `www_yz_she_goods`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `brand_id` int(11) NOT NULL DEFAULT 0 COMMENT '品牌ID',
  `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品货号/型号',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品图片JSON格式',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品描述',
  `price_new` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '全新品回收价格',
  `price_used` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '二手品回收价格',
  `price_damaged` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '有瑕疵回收价格',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '是否热门商品 1是 0否',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1上架 0下架',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_brand_id`(`brand_id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_code`(`code`) USING BTREE,
  INDEX `idx_is_hot`(`is_hot`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品表' ROW_FORMAT = Dynamic [ RunTime:0.023434s ]
[2025-08-05T20:20:23+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_order_status_logs` [ RunTime:0.000566s ]
[2025-08-05T20:20:23+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000530s ]
[2025-08-05T20:20:23+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000406s ]
[2025-08-05T20:20:23+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000309s ]
[2025-08-05T20:20:23+08:00][error] DEBUG：>>>>>>>>>
服务主体：0
IP：127.0.0.1
耗时（毫秒）：337
请求类型：POST
应用：adminapi
路由：/adminapi/addon/install/yz_she
请求参数：{"addon":"yz_she"}
错误信息：{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\service\\core\\addon\\CoreAddonInstallService.php","line":245,"message":"SQLSTATE[HY000]: General error: 1215 Cannot add foreign key constraint","trace":[{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\service\\admin\\addon\\AddonService.php","line":60,"function":"install","class":"app\\service\\core\\addon\\CoreAddonInstallService","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\controller\\addon\\Addon.php","line":56,"function":"install","class":"app\\service\\admin\\addon\\AddonService","type":"->"},{"function":"install","class":"app\\adminapi\\controller\\addon\\Addon","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Container.php","line":345,"function":"invokeArgs","class":"ReflectionMethod","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":110,"function":"invokeReflectMethod","class":"think\\Container","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\route\\dispatch\\{closure}","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":113,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\Dispatch.php","line":52,"function":"exec","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":755,"function":"run","class":"think\\route\\Dispatch","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminLog.php","line":58,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminLog","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckRole.php","line":31,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckRole","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckToken.php","line":32,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckToken","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":756,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":208,"function":"dispatch","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":198,"function":"dispatchToRoute","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\app\\{closure}","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AllowCrossDomain.php","line":36,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AllowCrossDomain","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":72,"function":"then","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-trace\\src\\TraceDebug.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\trace\\TraceDebug","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":199,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":162,"function":"runWithRequest","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\public\\index.php","line":24,"function":"run","class":"think\\Http","type":"->"}],"previous":null}
---------
[2025-08-05T20:24:01+08:00][sql] CONNECT:[ UseTime:0.017337s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:24:01+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.014756s ]
[2025-08-05T20:24:01+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.002317s ]
[2025-08-05T20:24:01+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.001739s ]
[2025-08-05T20:24:02+08:00][sql] CONNECT:[ UseTime:0.010242s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:24:02+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001549s ]
[2025-08-05T20:24:02+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001424s ]
[2025-08-05T20:24:02+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user_log` [ RunTime:0.006126s ]
[2025-08-05T20:24:02+08:00][sql] INSERT INTO `www_sys_user_log` SET `uid` = 1 , `username` = 'admin' , `url` = '/adminapi/addon/install/yz_she' , `params` = '{\"addon\":\"yz_she\"}' , `ip` = '127.0.0.1' , `type` = 'POST' , `operation` = '插件管理-安装插件' , `create_time` = 1754396642 [ RunTime:0.005438s ]
[2025-08-05T20:24:02+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.004250s ]
[2025-08-05T20:24:02+08:00][sql] SELECT * FROM `www_addon` WHERE  `key` = 'yz_she' LIMIT 1 [ RunTime:0.003455s ]
[2025-08-05T20:24:02+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000591s ]
[2025-08-05T20:24:02+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000199s ]
[2025-08-05T20:24:02+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000386s ]
[2025-08-05T20:24:02+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_brands` [ RunTime:0.000814s ]
[2025-08-05T20:24:02+08:00][sql] CREATE TABLE `www_yz_she_brands`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '品牌ID',
  `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '品牌名称',
  `logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '品牌Logo',
  `letter` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'A' COMMENT '首字母索引',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '是否热门品牌 1是 0否',
  `hot_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '热门品牌名字',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1启用 0禁用',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_letter`(`letter`) USING BTREE,
  INDEX `idx_is_hot`(`is_hot`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '品牌表' ROW_FORMAT = Dynamic [ RunTime:0.027403s ]
[2025-08-05T20:24:02+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_categories` [ RunTime:0.000271s ]
[2025-08-05T20:24:02+08:00][sql] CREATE TABLE `www_yz_she_categories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类图标',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类图片',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1启用 0禁用',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收分类表' ROW_FORMAT = Dynamic [ RunTime:0.013687s ]
[2025-08-05T20:24:02+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_category_accessories` [ RunTime:0.000378s ]
[2025-08-05T20:24:02+08:00][sql] CREATE TABLE `www_yz_she_category_accessories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `accessory_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配件名称',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分类配件配置表' ROW_FORMAT = Dynamic [ RunTime:0.014678s ]
[2025-08-05T20:24:02+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_category_photos` [ RunTime:0.000314s ]
[2025-08-05T20:24:02+08:00][sql] CREATE TABLE `www_yz_she_category_photos`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `photo_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片名称',
  `background_image` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'upload/background/default-photo-bg.png' COMMENT '背景图片',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分类照片配置表' ROW_FORMAT = Dynamic [ RunTime:0.014034s ]
[2025-08-05T20:24:02+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_express_log` [ RunTime:0.000365s ]
[2025-08-05T20:24:02+08:00][sql] CREATE TABLE `www_yz_she_express_log`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint(20) UNSIGNED NOT NULL COMMENT '订单ID',
  `waybill` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运单号',
  `shopbill` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家单号',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '运单状态描述',
  `type_code` tinyint(1) NULL DEFAULT NULL COMMENT '状态码：1待揽收，2运输中，3已签收，4拒收退回，99已取消',
  `weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '下单重量',
  `real_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '站点称重',
  `transfer_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '分拣称重',
  `cal_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '计费重量',
  `volume` decimal(10, 2) NULL DEFAULT NULL COMMENT '体积',
  `parse_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '体积换算重量',
  `total_freight` decimal(10, 2) NULL DEFAULT NULL COMMENT '运单总扣款费用',
  `freight` decimal(10, 2) NULL DEFAULT NULL COMMENT '快递费',
  `freight_insured` decimal(10, 2) NULL DEFAULT NULL COMMENT '保价费',
  `freight_haocai` decimal(10, 2) NULL DEFAULT NULL COMMENT '增值费用',
  `change_bill` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '换单号',
  `change_bill_freight` decimal(10, 2) NULL DEFAULT NULL COMMENT '逆向费',
  `fee_over` tinyint(1) NULL DEFAULT NULL COMMENT '订单扣费状态：1已扣费，0冻结',
  `courier_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递员姓名',
  `courier_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递员电话',
  `pickup_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '取件码',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '回调原始内容',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_waybill`(`waybill`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '物流回调日志表' ROW_FORMAT = DYNAMIC [ RunTime:0.014066s ]
[2025-08-05T20:24:02+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_goods` [ RunTime:0.000398s ]
[2025-08-05T20:24:02+08:00][sql] CREATE TABLE `www_yz_she_goods`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `brand_id` int(11) NOT NULL DEFAULT 0 COMMENT '品牌ID',
  `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品货号/型号',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品图片JSON格式',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品描述',
  `price_new` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '全新品回收价格',
  `price_used` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '二手品回收价格',
  `price_damaged` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '有瑕疵回收价格',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '是否热门商品 1是 0否',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1上架 0下架',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_brand_id`(`brand_id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_code`(`code`) USING BTREE,
  INDEX `idx_is_hot`(`is_hot`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品表' ROW_FORMAT = Dynamic [ RunTime:0.019850s ]
[2025-08-05T20:24:02+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_order_status_logs` [ RunTime:0.000354s ]
[2025-08-05T20:24:02+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000449s ]
[2025-08-05T20:24:02+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000383s ]
[2025-08-05T20:24:02+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.001765s ]
[2025-08-05T20:24:02+08:00][error] DEBUG：>>>>>>>>>
服务主体：0
IP：127.0.0.1
耗时（毫秒）：422
请求类型：POST
应用：adminapi
路由：/adminapi/addon/install/yz_she
请求参数：{"addon":"yz_she"}
错误信息：{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\service\\core\\addon\\CoreAddonInstallService.php","line":245,"message":"SQLSTATE[HY000]: General error: 1215 Cannot add foreign key constraint","trace":[{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\service\\admin\\addon\\AddonService.php","line":60,"function":"install","class":"app\\service\\core\\addon\\CoreAddonInstallService","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\controller\\addon\\Addon.php","line":56,"function":"install","class":"app\\service\\admin\\addon\\AddonService","type":"->"},{"function":"install","class":"app\\adminapi\\controller\\addon\\Addon","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Container.php","line":345,"function":"invokeArgs","class":"ReflectionMethod","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":110,"function":"invokeReflectMethod","class":"think\\Container","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\route\\dispatch\\{closure}","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":113,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\Dispatch.php","line":52,"function":"exec","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":755,"function":"run","class":"think\\route\\Dispatch","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminLog.php","line":58,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminLog","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckRole.php","line":31,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckRole","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckToken.php","line":32,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckToken","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":756,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":208,"function":"dispatch","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":198,"function":"dispatchToRoute","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\app\\{closure}","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AllowCrossDomain.php","line":36,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AllowCrossDomain","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":72,"function":"then","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-trace\\src\\TraceDebug.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\trace\\TraceDebug","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":199,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":162,"function":"runWithRequest","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\public\\index.php","line":24,"function":"run","class":"think\\Http","type":"->"}],"previous":null}
---------
[2025-08-05T20:26:00+08:00][sql] CONNECT:[ UseTime:0.001688s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:00+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.004278s ]
[2025-08-05T20:26:00+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000404s ]
[2025-08-05T20:26:00+08:00][sql] CONNECT:[ UseTime:0.001479s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:00+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001104s ]
[2025-08-05T20:26:00+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001312s ]
[2025-08-05T20:26:01+08:00][sql] CONNECT:[ UseTime:0.001013s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:01+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000778s ]
[2025-08-05T20:26:01+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000616s ]
[2025-08-05T20:26:01+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.001480s ]
[2025-08-05T20:26:01+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000194s ]
[2025-08-05T20:26:01+08:00][sql] SHOW FULL COLUMNS FROM `www_diy_theme` [ RunTime:0.001180s ]
[2025-08-05T20:26:01+08:00][sql] SELECT `id`,`title`,`theme`,`addon` FROM `www_diy_theme` WHERE  `type` = 'app'  AND `is_selected` = 1 [ RunTime:0.000551s ]
[2025-08-05T20:26:07+08:00][sql] CONNECT:[ UseTime:0.010906s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:07+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000799s ]
[2025-08-05T20:26:07+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001062s ]
[2025-08-05T20:26:07+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000651s ]
[2025-08-05T20:26:07+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000341s ]
[2025-08-05T20:26:07+08:00][sql] SHOW FULL COLUMNS FROM `www_diy_theme` [ RunTime:0.001461s ]
[2025-08-05T20:26:07+08:00][sql] SELECT `id`,`title`,`theme`,`addon` FROM `www_diy_theme` WHERE  `type` = 'app'  AND `is_selected` = 1 [ RunTime:0.000392s ]
[2025-08-05T20:26:08+08:00][sql] CONNECT:[ UseTime:0.011860s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:08+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.002477s ]
[2025-08-05T20:26:08+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.002458s ]
[2025-08-05T20:26:08+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_poster` [ RunTime:0.004172s ]
[2025-08-05T20:26:08+08:00][sql] CONNECT:[ UseTime:0.000889s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:08+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000665s ]
[2025-08-05T20:26:08+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000451s ]
[2025-08-05T20:26:08+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_poster` [ RunTime:0.000996s ]
[2025-08-05T20:26:08+08:00][sql] SELECT COUNT(*) AS think_count FROM `www_sys_poster` WHERE  `id` > 0 [ RunTime:0.001449s ]
[2025-08-05T20:26:08+08:00][sql] SELECT `id`,`name`,`type`,`channel`,`value`,`status`,`is_default`,`create_time`,`update_time`,`addon` FROM `www_sys_poster` WHERE  `id` > 0 ORDER BY `update_time` DESC LIMIT 0,10 [ RunTime:0.000876s ]
[2025-08-05T20:26:10+08:00][sql] CONNECT:[ UseTime:0.002430s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:10+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000619s ]
[2025-08-05T20:26:10+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000583s ]
[2025-08-05T20:26:10+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_attachment` [ RunTime:0.000695s ]
[2025-08-05T20:26:10+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_attachment_category` [ RunTime:0.001541s ]
[2025-08-05T20:26:10+08:00][sql] SELECT `id`,`name`,`type` FROM `www_sys_attachment_category` WHERE  `type` = 'image' ORDER BY `id` DESC [ RunTime:0.000312s ]
[2025-08-05T20:26:10+08:00][sql] CONNECT:[ UseTime:0.011235s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:10+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000943s ]
[2025-08-05T20:26:10+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000684s ]
[2025-08-05T20:26:10+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_attachment` [ RunTime:0.001238s ]
[2025-08-05T20:26:10+08:00][sql] SELECT COUNT(*) AS think_count FROM `www_sys_attachment` WHERE  `att_type` = 'image' [ RunTime:0.000344s ]
[2025-08-05T20:26:10+08:00][sql] CONNECT:[ UseTime:0.000971s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:10+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000621s ]
[2025-08-05T20:26:10+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000683s ]
[2025-08-05T20:26:10+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_attachment` [ RunTime:0.000467s ]
[2025-08-05T20:26:10+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_attachment_category` [ RunTime:0.000995s ]
[2025-08-05T20:26:10+08:00][sql] SELECT `id`,`name`,`type` FROM `www_sys_attachment_category` WHERE  `type` = 'video' ORDER BY `id` DESC [ RunTime:0.000188s ]
[2025-08-05T20:26:10+08:00][sql] CONNECT:[ UseTime:0.001044s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:10+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000613s ]
[2025-08-05T20:26:10+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000543s ]
[2025-08-05T20:26:10+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_attachment` [ RunTime:0.001621s ]
[2025-08-05T20:26:10+08:00][sql] SELECT COUNT(*) AS think_count FROM `www_sys_attachment` WHERE  `att_type` = 'video' [ RunTime:0.000271s ]
[2025-08-05T20:26:10+08:00][sql] CONNECT:[ UseTime:0.000996s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:10+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001847s ]
[2025-08-05T20:26:10+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000747s ]
[2025-08-05T20:26:10+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000730s ]
[2025-08-05T20:26:10+08:00][sql] SELECT `id`,`title`,`key`,`desc`,`version`,`status`,`icon`,`create_time`,`install_time` FROM `www_addon` WHERE  `type` = 'app' ORDER BY `id` DESC [ RunTime:0.000243s ]
[2025-08-05T20:26:13+08:00][sql] CONNECT:[ UseTime:0.002047s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:13+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000761s ]
[2025-08-05T20:26:13+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001575s ]
[2025-08-05T20:26:13+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.001335s ]
[2025-08-05T20:26:14+08:00][sql] CONNECT:[ UseTime:0.011426s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:14+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001097s ]
[2025-08-05T20:26:14+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001251s ]
[2025-08-05T20:26:14+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.001070s ]
[2025-08-05T20:26:14+08:00][sql] SELECT `id`,`title`,`key`,`desc`,`version`,`status`,`icon`,`create_time`,`install_time` FROM `www_addon` WHERE  `type` = 'app' ORDER BY `id` DESC [ RunTime:0.000312s ]
[2025-08-05T20:26:15+08:00][sql] CONNECT:[ UseTime:0.001115s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:15+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000817s ]
[2025-08-05T20:26:15+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000761s ]
[2025-08-05T20:26:15+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000706s ]
[2025-08-05T20:26:15+08:00][sql] CONNECT:[ UseTime:0.000980s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:15+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000861s ]
[2025-08-05T20:26:15+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.003675s ]
[2025-08-05T20:26:15+08:00][sql] SHOW FULL COLUMNS FROM `www_diy_route` [ RunTime:0.001869s ]
[2025-08-05T20:26:15+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000916s ]
[2025-08-05T20:26:15+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`cover` FROM `www_addon` WHERE  0 = 1  AND `status` = 1 [ RunTime:0.000401s ]
[2025-08-05T20:26:15+08:00][sql] CONNECT:[ UseTime:0.001120s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:15+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.003601s ]
[2025-08-05T20:26:15+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001248s ]
[2025-08-05T20:26:15+08:00][sql] SHOW FULL COLUMNS FROM `www_diy_route` [ RunTime:0.000958s ]
[2025-08-05T20:26:15+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000971s ]
[2025-08-05T20:26:15+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`cover` FROM `www_addon` WHERE  0 = 1  AND `status` = 1 [ RunTime:0.000188s ]
[2025-08-05T20:26:15+08:00][sql] CONNECT:[ UseTime:0.001024s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:15+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001936s ]
[2025-08-05T20:26:15+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001144s ]
[2025-08-05T20:26:16+08:00][sql] CONNECT:[ UseTime:0.005943s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:16+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000803s ]
[2025-08-05T20:26:16+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000661s ]
[2025-08-05T20:26:16+08:00][sql] SHOW FULL COLUMNS FROM `www_diy_page` [ RunTime:0.001194s ]
[2025-08-05T20:26:16+08:00][sql] SELECT COUNT(*) AS think_count FROM `www_diy_page` WHERE  `id` > 0 [ RunTime:0.000325s ]
[2025-08-05T20:26:16+08:00][sql] CONNECT:[ UseTime:0.000954s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:16+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000978s ]
[2025-08-05T20:26:16+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000766s ]
[2025-08-05T20:26:16+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000568s ]
[2025-08-05T20:26:16+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`cover` FROM `www_addon` WHERE  0 = 1  AND `status` = 1 [ RunTime:0.000207s ]
[2025-08-05T20:26:16+08:00][sql] CONNECT:[ UseTime:0.000835s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:16+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000834s ]
[2025-08-05T20:26:16+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.003223s ]
[2025-08-05T20:26:16+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000560s ]
[2025-08-05T20:26:17+08:00][sql] CONNECT:[ UseTime:0.001017s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:17+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000685s ]
[2025-08-05T20:26:17+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000672s ]
[2025-08-05T20:26:17+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000585s ]
[2025-08-05T20:26:17+08:00][sql] CONNECT:[ UseTime:0.009980s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:17+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.004202s ]
[2025-08-05T20:26:17+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000814s ]
[2025-08-05T20:26:17+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.002657s ]
[2025-08-05T20:26:17+08:00][sql] SHOW FULL COLUMNS FROM `www_diy_page` [ RunTime:0.001530s ]
[2025-08-05T20:26:17+08:00][sql] SELECT `id`,`title`,`page_title`,`name`,`template`,`type`,`mode`,`value`,`is_default`,`is_change`,`share`,`visit_count` FROM `www_diy_page` WHERE  `name` = 'DIY_MEMBER_INDEX'  AND `is_default` = 1 LIMIT 1 [ RunTime:0.000386s ]
[2025-08-05T20:26:17+08:00][sql] SHOW FULL COLUMNS FROM `www_diy_route` [ RunTime:0.001256s ]
[2025-08-05T20:26:17+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000731s ]
[2025-08-05T20:26:17+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`cover` FROM `www_addon` WHERE  0 = 1  AND `status` = 1 [ RunTime:0.000409s ]
[2025-08-05T20:26:20+08:00][sql] CONNECT:[ UseTime:0.001161s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:20+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001324s ]
[2025-08-05T20:26:20+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001381s ]
[2025-08-05T20:26:20+08:00][sql] SHOW FULL COLUMNS FROM `www_diy_page` [ RunTime:0.002502s ]
[2025-08-05T20:26:20+08:00][sql] SELECT COUNT(*) AS think_count FROM `www_diy_page` WHERE  `id` > 0 [ RunTime:0.000615s ]
[2025-08-05T20:26:21+08:00][sql] CONNECT:[ UseTime:0.000911s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:21+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000615s ]
[2025-08-05T20:26:21+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000581s ]
[2025-08-05T20:26:21+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.001433s ]
[2025-08-05T20:26:21+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`cover` FROM `www_addon` WHERE  0 = 1  AND `status` = 1 [ RunTime:0.000277s ]
[2025-08-05T20:26:21+08:00][sql] CONNECT:[ UseTime:0.000914s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:21+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001296s ]
[2025-08-05T20:26:21+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000755s ]
[2025-08-05T20:26:21+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.001279s ]
[2025-08-05T20:26:21+08:00][sql] CONNECT:[ UseTime:0.010827s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:21+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001245s ]
[2025-08-05T20:26:21+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000964s ]
[2025-08-05T20:26:21+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.001576s ]
[2025-08-05T20:26:21+08:00][sql] SHOW FULL COLUMNS FROM `www_diy_page` [ RunTime:0.000859s ]
[2025-08-05T20:26:21+08:00][sql] SELECT `id`,`title`,`page_title`,`name`,`template`,`type`,`mode`,`value`,`is_default`,`is_change`,`share`,`visit_count` FROM `www_diy_page` WHERE  `name` = 'DIY_INDEX'  AND `is_default` = 1 LIMIT 1 [ RunTime:0.000319s ]
[2025-08-05T20:26:21+08:00][sql] SHOW FULL COLUMNS FROM `www_diy_route` [ RunTime:0.000575s ]
[2025-08-05T20:26:21+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000550s ]
[2025-08-05T20:26:21+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`cover` FROM `www_addon` WHERE  0 = 1  AND `status` = 1 [ RunTime:0.000257s ]
[2025-08-05T20:26:23+08:00][sql] CONNECT:[ UseTime:0.000908s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:23+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000565s ]
[2025-08-05T20:26:23+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000595s ]
[2025-08-05T20:26:23+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000603s ]
[2025-08-05T20:26:23+08:00][sql] CONNECT:[ UseTime:0.001213s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:23+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000791s ]
[2025-08-05T20:26:23+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000645s ]
[2025-08-05T20:26:23+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000809s ]
[2025-08-05T20:26:23+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`cover` FROM `www_addon` WHERE  0 = 1  AND `status` = 1 [ RunTime:0.000411s ]
[2025-08-05T20:26:23+08:00][sql] CONNECT:[ UseTime:0.000874s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:23+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000581s ]
[2025-08-05T20:26:23+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000564s ]
[2025-08-05T20:26:23+08:00][sql] SHOW FULL COLUMNS FROM `www_diy_page` [ RunTime:0.002971s ]
[2025-08-05T20:26:23+08:00][sql] SELECT COUNT(*) AS think_count FROM `www_diy_page` WHERE  `id` > 0 [ RunTime:0.001432s ]
[2025-08-05T20:26:24+08:00][sql] CONNECT:[ UseTime:0.001635s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:24+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001121s ]
[2025-08-05T20:26:24+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000818s ]
[2025-08-05T20:26:24+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.001309s ]
[2025-08-05T20:26:24+08:00][sql] SHOW FULL COLUMNS FROM `www_diy_page` [ RunTime:0.000605s ]
[2025-08-05T20:26:24+08:00][sql] SELECT `id`,`title`,`page_title`,`name`,`template`,`type`,`mode`,`value`,`is_default`,`is_change`,`share`,`visit_count` FROM `www_diy_page` WHERE  `name` = 'DIY_INDEX'  AND `is_default` = 1 LIMIT 1 [ RunTime:0.000284s ]
[2025-08-05T20:26:24+08:00][sql] SHOW FULL COLUMNS FROM `www_diy_route` [ RunTime:0.000498s ]
[2025-08-05T20:26:24+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000541s ]
[2025-08-05T20:26:24+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`cover` FROM `www_addon` WHERE  0 = 1  AND `status` = 1 [ RunTime:0.000229s ]
[2025-08-05T20:26:25+08:00][sql] CONNECT:[ UseTime:0.000900s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:25+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000598s ]
[2025-08-05T20:26:25+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001335s ]
[2025-08-05T20:26:25+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000591s ]
[2025-08-05T20:26:25+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`cover` FROM `www_addon` WHERE  0 = 1  AND `status` = 1 [ RunTime:0.000204s ]
[2025-08-05T20:26:25+08:00][sql] SHOW FULL COLUMNS FROM `www_diy_page` [ RunTime:0.000601s ]
[2025-08-05T20:26:25+08:00][sql] SELECT `id`,`title`,`page_title`,`name`,`template`,`type`,`mode`,`is_default`,`share`,`visit_count`,`create_time`,`update_time` FROM `www_diy_page` WHERE  ( `type` = 'DIY_PAGE' )  OR ( `type` <> 'DIY_PAGE' AND `is_default` = 0 ) ORDER BY `update_time` DESC [ RunTime:0.000288s ]
[2025-08-05T20:26:27+08:00][sql] CONNECT:[ UseTime:0.000999s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:27+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.002648s ]
[2025-08-05T20:26:27+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000678s ]
[2025-08-05T20:26:27+08:00][sql] SHOW FULL COLUMNS FROM `www_diy_form` [ RunTime:0.001245s ]
[2025-08-05T20:26:27+08:00][sql] SELECT COUNT(*) AS think_count FROM `www_diy_form` WHERE  `status` = 1 [ RunTime:0.000271s ]
[2025-08-05T20:26:27+08:00][sql] CONNECT:[ UseTime:0.000959s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:26:27+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000733s ]
[2025-08-05T20:26:27+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000794s ]
[2025-08-05T20:28:48+08:00][sql] CONNECT:[ UseTime:0.000855s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:28:48+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000783s ]
[2025-08-05T20:28:48+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000243s ]
[2025-08-05T20:28:50+08:00][sql] CONNECT:[ UseTime:0.011111s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:28:50+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000675s ]
[2025-08-05T20:28:50+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000659s ]
[2025-08-05T20:28:52+08:00][sql] CONNECT:[ UseTime:0.013555s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:28:52+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.002320s ]
[2025-08-05T20:28:52+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.002188s ]
[2025-08-05T20:28:52+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000721s ]
[2025-08-05T20:28:52+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`author`,`version`,`install_time`,`update_time`,`cover` FROM `www_addon` [ RunTime:0.000266s ]
[2025-08-05T20:28:52+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000555s ]
[2025-08-05T20:28:52+08:00][sql] CONNECT:[ UseTime:0.006527s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:28:52+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000927s ]
[2025-08-05T20:28:52+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000847s ]
[2025-08-05T20:28:52+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000586s ]
[2025-08-05T20:28:52+08:00][error] DEBUG：>>>>>>>>>
服务主体：0
IP：127.0.0.1
耗时（毫秒）：261
请求类型：GET
应用：adminapi
路由：/adminapi/niucloud/authinfo
请求参数：[]
错误信息：{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":148,"message":"\u6388\u6743\u7801\u6216\u6388\u6743\u79d8\u94a5\u9519\u8bef","trace":[{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\guzzle\\src\\RetryMiddleware.php","line":84,"function":"core\\util\\niucloud\\{closure}","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\FulfilledPromise.php","line":41,"function":"GuzzleHttp\\{closure}","class":"GuzzleHttp\\RetryMiddleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php","line":48,"function":"GuzzleHttp\\Promise\\{closure}","class":"GuzzleHttp\\Promise\\FulfilledPromise","type":"::"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":248,"function":"run","class":"GuzzleHttp\\Promise\\TaskQueue","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":224,"function":"invokeWaitFn","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":269,"function":"waitIfPending","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":226,"function":"invokeWaitList","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":62,"function":"waitIfPending","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\guzzle\\src\\Client.php","line":187,"function":"wait","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\http\\HasHttpRequests.php","line":97,"function":"request","class":"GuzzleHttp\\Client","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":110,"function":"toRequest","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":193,"function":"request","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\service\\core\\niucloud\\CoreAuthService.php","line":31,"function":"httpGet","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\controller\\niucloud\\Module.php","line":35,"function":"getAuthInfo","class":"app\\service\\core\\niucloud\\CoreAuthService","type":"->"},{"function":"authorize","class":"app\\adminapi\\controller\\niucloud\\Module","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Container.php","line":345,"function":"invokeArgs","class":"ReflectionMethod","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":110,"function":"invokeReflectMethod","class":"think\\Container","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\route\\dispatch\\{closure}","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":113,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\Dispatch.php","line":52,"function":"exec","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":755,"function":"run","class":"think\\route\\Dispatch","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminLog.php","line":58,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminLog","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckRole.php","line":31,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckRole","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckToken.php","line":32,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckToken","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":756,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":208,"function":"dispatch","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":198,"function":"dispatchToRoute","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\app\\{closure}","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AllowCrossDomain.php","line":36,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AllowCrossDomain","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":72,"function":"then","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-trace\\src\\TraceDebug.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\trace\\TraceDebug","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":199,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":162,"function":"runWithRequest","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\public\\index.php","line":24,"function":"run","class":"think\\Http","type":"->"}],"previous":null}
---------
[2025-08-05T20:28:52+08:00][sql] CONNECT:[ UseTime:0.010739s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:28:52+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000761s ]
[2025-08-05T20:28:52+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000621s ]
[2025-08-05T20:28:52+08:00][sql] CONNECT:[ UseTime:0.010075s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:28:52+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000900s ]
[2025-08-05T20:28:52+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000748s ]
[2025-08-05T20:28:52+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.001969s ]
[2025-08-05T20:28:54+08:00][sql] CONNECT:[ UseTime:0.010240s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:28:54+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000731s ]
[2025-08-05T20:28:54+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000594s ]
[2025-08-05T20:28:54+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000499s ]
[2025-08-05T20:28:54+08:00][sql] CONNECT:[ UseTime:0.010680s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:28:54+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000997s ]
[2025-08-05T20:28:54+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000831s ]
[2025-08-05T20:28:54+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000673s ]
[2025-08-05T20:28:54+08:00][sql] CONNECT:[ UseTime:0.011215s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:28:54+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000951s ]
[2025-08-05T20:28:54+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000657s ]
[2025-08-05T20:28:54+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000638s ]
[2025-08-05T20:28:54+08:00][sql] CONNECT:[ UseTime:0.001146s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:28:54+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000802s ]
[2025-08-05T20:28:54+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000934s ]
[2025-08-05T20:28:54+08:00][sql] CONNECT:[ UseTime:0.011159s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:28:54+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001069s ]
[2025-08-05T20:28:54+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000618s ]
[2025-08-05T20:28:54+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000836s ]
[2025-08-05T20:28:54+08:00][error] DEBUG：>>>>>>>>>
服务主体：0
IP：127.0.0.1
耗时（毫秒）：242
请求类型：GET
应用：adminapi
路由：/adminapi/niucloud/authinfo
请求参数：[]
错误信息：{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":148,"message":"\u6388\u6743\u7801\u6216\u6388\u6743\u79d8\u94a5\u9519\u8bef","trace":[{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\guzzle\\src\\RetryMiddleware.php","line":84,"function":"core\\util\\niucloud\\{closure}","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\FulfilledPromise.php","line":41,"function":"GuzzleHttp\\{closure}","class":"GuzzleHttp\\RetryMiddleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php","line":48,"function":"GuzzleHttp\\Promise\\{closure}","class":"GuzzleHttp\\Promise\\FulfilledPromise","type":"::"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":248,"function":"run","class":"GuzzleHttp\\Promise\\TaskQueue","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":224,"function":"invokeWaitFn","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":269,"function":"waitIfPending","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":226,"function":"invokeWaitList","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":62,"function":"waitIfPending","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\guzzle\\src\\Client.php","line":187,"function":"wait","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\http\\HasHttpRequests.php","line":97,"function":"request","class":"GuzzleHttp\\Client","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":110,"function":"toRequest","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":193,"function":"request","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\service\\core\\niucloud\\CoreAuthService.php","line":31,"function":"httpGet","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\controller\\niucloud\\Module.php","line":35,"function":"getAuthInfo","class":"app\\service\\core\\niucloud\\CoreAuthService","type":"->"},{"function":"authorize","class":"app\\adminapi\\controller\\niucloud\\Module","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Container.php","line":345,"function":"invokeArgs","class":"ReflectionMethod","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":110,"function":"invokeReflectMethod","class":"think\\Container","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\route\\dispatch\\{closure}","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":113,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\Dispatch.php","line":52,"function":"exec","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":755,"function":"run","class":"think\\route\\Dispatch","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminLog.php","line":58,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminLog","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckRole.php","line":31,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckRole","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckToken.php","line":32,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckToken","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":756,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":208,"function":"dispatch","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":198,"function":"dispatchToRoute","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\app\\{closure}","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AllowCrossDomain.php","line":36,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AllowCrossDomain","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":72,"function":"then","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-trace\\src\\TraceDebug.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\trace\\TraceDebug","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":199,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":162,"function":"runWithRequest","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\public\\index.php","line":24,"function":"run","class":"think\\Http","type":"->"}],"previous":null}
---------
[2025-08-05T20:28:54+08:00][sql] CONNECT:[ UseTime:0.010781s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:28:54+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000623s ]
[2025-08-05T20:28:54+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000637s ]
[2025-08-05T20:28:54+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000710s ]
[2025-08-05T20:28:56+08:00][sql] CONNECT:[ UseTime:0.011256s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:28:56+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001109s ]
[2025-08-05T20:28:56+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000660s ]
[2025-08-05T20:28:56+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user_log` [ RunTime:0.002125s ]
[2025-08-05T20:28:56+08:00][sql] INSERT INTO `www_sys_user_log` SET `uid` = 1 , `username` = 'admin' , `url` = '/adminapi/addon/install/yz_she' , `params` = '{\"addon\":\"yz_she\"}' , `ip` = '127.0.0.1' , `type` = 'POST' , `operation` = '插件管理-安装插件' , `create_time` = 1754396936 [ RunTime:0.000781s ]
[2025-08-05T20:28:56+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000895s ]
[2025-08-05T20:28:56+08:00][sql] SELECT * FROM `www_addon` WHERE  `key` = 'yz_she' LIMIT 1 [ RunTime:0.000416s ]
[2025-08-05T20:28:56+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000340s ]
[2025-08-05T20:28:56+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000401s ]
[2025-08-05T20:28:56+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000338s ]
[2025-08-05T20:28:56+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_brands` [ RunTime:0.000347s ]
[2025-08-05T20:28:56+08:00][sql] CREATE TABLE `www_yz_she_brands`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '品牌ID',
  `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '品牌名称',
  `logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '品牌Logo',
  `letter` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'A' COMMENT '首字母索引',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '是否热门品牌 1是 0否',
  `hot_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '热门品牌名字',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1启用 0禁用',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_letter`(`letter`) USING BTREE,
  INDEX `idx_is_hot`(`is_hot`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '品牌表' ROW_FORMAT = Dynamic [ RunTime:0.019418s ]
[2025-08-05T20:28:56+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_categories` [ RunTime:0.000547s ]
[2025-08-05T20:28:56+08:00][sql] CREATE TABLE `www_yz_she_categories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类图标',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类图片',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1启用 0禁用',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收分类表' ROW_FORMAT = Dynamic [ RunTime:0.014810s ]
[2025-08-05T20:28:56+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_category_accessories` [ RunTime:0.000603s ]
[2025-08-05T20:28:56+08:00][sql] CREATE TABLE `www_yz_she_category_accessories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `accessory_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配件名称',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分类配件配置表' ROW_FORMAT = Dynamic [ RunTime:0.014527s ]
[2025-08-05T20:28:56+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_category_photos` [ RunTime:0.000390s ]
[2025-08-05T20:28:56+08:00][sql] CREATE TABLE `www_yz_she_category_photos`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `photo_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片名称',
  `background_image` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'upload/background/default-photo-bg.png' COMMENT '背景图片',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分类照片配置表' ROW_FORMAT = Dynamic [ RunTime:0.014004s ]
[2025-08-05T20:28:56+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_express_log` [ RunTime:0.000381s ]
[2025-08-05T20:28:56+08:00][sql] CREATE TABLE `www_yz_she_express_log`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint(20) UNSIGNED NOT NULL COMMENT '订单ID',
  `waybill` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运单号',
  `shopbill` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家单号',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '运单状态描述',
  `type_code` tinyint(1) NULL DEFAULT NULL COMMENT '状态码：1待揽收，2运输中，3已签收，4拒收退回，99已取消',
  `weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '下单重量',
  `real_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '站点称重',
  `transfer_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '分拣称重',
  `cal_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '计费重量',
  `volume` decimal(10, 2) NULL DEFAULT NULL COMMENT '体积',
  `parse_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '体积换算重量',
  `total_freight` decimal(10, 2) NULL DEFAULT NULL COMMENT '运单总扣款费用',
  `freight` decimal(10, 2) NULL DEFAULT NULL COMMENT '快递费',
  `freight_insured` decimal(10, 2) NULL DEFAULT NULL COMMENT '保价费',
  `freight_haocai` decimal(10, 2) NULL DEFAULT NULL COMMENT '增值费用',
  `change_bill` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '换单号',
  `change_bill_freight` decimal(10, 2) NULL DEFAULT NULL COMMENT '逆向费',
  `fee_over` tinyint(1) NULL DEFAULT NULL COMMENT '订单扣费状态：1已扣费，0冻结',
  `courier_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递员姓名',
  `courier_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递员电话',
  `pickup_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '取件码',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '回调原始内容',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_waybill`(`waybill`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '物流回调日志表' ROW_FORMAT = DYNAMIC [ RunTime:0.013682s ]
[2025-08-05T20:28:56+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_goods` [ RunTime:0.000388s ]
[2025-08-05T20:28:56+08:00][sql] CREATE TABLE `www_yz_she_goods`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `brand_id` int(11) NOT NULL DEFAULT 0 COMMENT '品牌ID',
  `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品货号/型号',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品图片JSON格式',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品描述',
  `price_new` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '全新品回收价格',
  `price_used` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '二手品回收价格',
  `price_damaged` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '有瑕疵回收价格',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '是否热门商品 1是 0否',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1上架 0下架',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_brand_id`(`brand_id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_code`(`code`) USING BTREE,
  INDEX `idx_is_hot`(`is_hot`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品表' ROW_FORMAT = Dynamic [ RunTime:0.018881s ]
[2025-08-05T20:28:56+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_quote_orders` [ RunTime:0.000759s ]
[2025-08-05T20:28:56+08:00][sql] CREATE TABLE `www_yz_she_quote_orders`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `brand_id` int(11) NULL DEFAULT NULL COMMENT '品牌ID',
  `product_id` int(11) NULL DEFAULT NULL COMMENT '商品ID',
  `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `product_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品编码',
  `product_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品图片/品牌图片',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单状态:1=估价中,2=待确认,3=待发货,4=已完成,5=已取消',
  `quote_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '估价金额',
  `user_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户备注',
  `admin_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '管理员备注',
  `admin_id` int(11) NULL DEFAULT NULL COMMENT '估价管理员ID',
  `quote_time` datetime NULL DEFAULT NULL COMMENT '估价时间',
  `confirm_time` datetime NULL DEFAULT NULL COMMENT '用户确认时间',
  `ship_time` datetime NULL DEFAULT NULL COMMENT '用户发货时间',
  `complete_time` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `cancel_time` datetime NULL DEFAULT NULL COMMENT '取消时间',
  `cancel_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '取消原因',
  `auto_cancel_time` datetime NULL DEFAULT NULL COMMENT '自动取消时间(确认后48小时)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_no`(`order_no`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  INDEX `brand_id`(`brand_id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `admin_id`(`admin_id`) USING BTREE,
  INDEX `create_time`(`create_time`) USING BTREE,
  INDEX `auto_cancel_time`(`auto_cancel_time`) USING BTREE,
  INDEX `idx_orders_user_status_time`(`user_id`, `status`, `create_time`) USING BTREE,
  INDEX `idx_orders_status_time`(`status`, `create_time`) USING BTREE,
  INDEX `idx_orders_auto_cancel`(`status`, `auto_cancel_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 37 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价订单主表' ROW_FORMAT = Dynamic [ RunTime:0.027201s ]
[2025-08-05T20:28:56+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_order_status_logs` [ RunTime:0.000464s ]
[2025-08-05T20:28:56+08:00][sql] CREATE TABLE `www_yz_she_order_status_logs`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `from_status` tinyint(1) NULL DEFAULT NULL COMMENT '原状态',
  `to_status` tinyint(1) NOT NULL COMMENT '新状态',
  `operator_id` int(11) NULL DEFAULT NULL COMMENT '操作人ID',
  `operator_type` tinyint(1) NOT NULL COMMENT '操作人类型:1=用户,2=管理员,3=系统',
  `change_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更原因',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `operator_id`(`operator_id`) USING BTREE,
  INDEX `create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价订单状态变更日志表' ROW_FORMAT = Dynamic [ RunTime:0.016510s ]
[2025-08-05T20:28:56+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_quote_accessories` [ RunTime:0.000627s ]
[2025-08-05T20:28:56+08:00][sql] CREATE TABLE `www_yz_she_quote_accessories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配件ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `accessory_config_id` int(11) NULL DEFAULT NULL COMMENT '配件配置ID',
  `accessory_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配件名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `accessory_config_id`(`accessory_config_id`) USING BTREE,
  INDEX `idx_accessories_order`(`quote_order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价订单配件表' ROW_FORMAT = Dynamic [ RunTime:0.016244s ]
[2025-08-05T20:28:56+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_quote_photos` [ RunTime:0.000538s ]
[2025-08-05T20:28:56+08:00][sql] CREATE TABLE `www_yz_she_quote_photos`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '照片ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `photo_config_id` int(11) NULL DEFAULT NULL COMMENT '照片配置ID',
  `photo_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片类型',
  `photo_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片名称',
  `photo_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片URL',
  `is_defect` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为瑕疵照片:0=否,1=是',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `photo_config_id`(`photo_config_id`) USING BTREE,
  INDEX `photo_type`(`photo_type`) USING BTREE,
  INDEX `is_defect`(`is_defect`) USING BTREE,
  INDEX `idx_photos_order_type`(`quote_order_id`, `photo_type`, `sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价订单照片表' ROW_FORMAT = Dynamic [ RunTime:0.019366s ]
[2025-08-05T20:28:56+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_quote_records` [ RunTime:0.000458s ]
[2025-08-05T20:28:56+08:00][sql] CREATE TABLE `www_yz_she_quote_records`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `admin_id` int(11) NOT NULL COMMENT '估价管理员ID',
  `condition_score` int(3) NULL DEFAULT NULL COMMENT '成色评分(0-100)',
  `quote_price` decimal(10, 2) NOT NULL COMMENT '估价金额',
  `quote_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '估价说明',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `admin_id`(`admin_id`) USING BTREE,
  INDEX `create_time`(`create_time`) USING BTREE,
  INDEX `idx_records_order_time`(`quote_order_id`, `create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价记录表' ROW_FORMAT = Dynamic [ RunTime:0.016252s ]
[2025-08-05T20:28:56+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_recycle_order_logs` [ RunTime:0.000899s ]
[2025-08-05T20:28:56+08:00][sql] CREATE TABLE `www_yz_she_recycle_order_logs`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `recycle_order_id` int(11) NOT NULL COMMENT '回收订单ID',
  `from_status` tinyint(1) NULL DEFAULT NULL COMMENT '原状态',
  `to_status` tinyint(1) NOT NULL COMMENT '新状态',
  `operator_id` int(11) NULL DEFAULT NULL COMMENT '操作人ID',
  `operator_type` tinyint(1) NOT NULL COMMENT '操作人类型:1=用户,2=管理员,3=系统',
  `change_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更原因',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `extra_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '额外数据JSON',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_recycle_order_id`(`recycle_order_id`) USING BTREE,
  INDEX `idx_operator_id`(`operator_id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 315 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收订单状态变更日志表' ROW_FORMAT = DYNAMIC [ RunTime:0.015945s ]
[2025-08-05T20:28:56+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_recycle_orders` [ RunTime:0.000461s ]
[2025-08-05T20:28:56+08:00][sql] CREATE TABLE `www_yz_she_recycle_orders`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '回收订单ID',
  `order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '回收订单编号',
  `quote_order_id` int(11) NULL DEFAULT NULL COMMENT '关联的估价订单ID（从估价订单创建时有值）',
  `member_id` int(11) NOT NULL COMMENT '会员ID（对应member表）',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `brand_id` int(11) NULL DEFAULT NULL COMMENT '品牌ID',
  `product_id` int(11) NULL DEFAULT NULL COMMENT '商品ID',
  `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `product_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品编码',
  `product_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品图片',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单状态:1=待取件,2=待收货,3=待质检,4=待确认,5=待退回,6=已退回,7=已完成,8=已取消',
  `source_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单来源:1=估价订单确认,2=直接回收,3=批量下单',
  `quantity` int(11) NOT NULL DEFAULT 1 COMMENT '回收数量',
  `expected_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '预期回收价格（用户选择的价格）',
  `final_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '最终回收价格（质检后确定）',
  `voucher_id` int(11) NULL DEFAULT NULL COMMENT '使用的加价券ID（对应www_yz_she_voucher表）',
  `voucher_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '加价券金额',
  `total_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '最终结算金额',
  `delivery_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '配送方式:1=快递上门,2=自行寄出',
  `pickup_address_id` int(11) NULL DEFAULT NULL COMMENT '上门取件地址ID（对应www_member_address表）',
  `pickup_time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '期望上门时间',
  `pickup_contact_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系人姓名',
  `pickup_contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系人电话',
  `pickup_address_detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '详细地址信息',
  `express_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递回调ID',
  `express_company` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递公司',
  `express_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递单号',
  `express_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '快递费用',
  `express_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '快递状态:0=暂无轨迹,1=已揽收,2=运输中,3=已签收,4=异常',
  `quality_check_admin_id` int(11) NULL DEFAULT NULL COMMENT '质检员ID',
  `quality_score` int(3) NULL DEFAULT NULL COMMENT '质检评分(0-100)',
  `quality_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '质检说明',
  `quality_images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '质检照片JSON',
  `settlement_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '结算方式:1=余额结算',
  `settlement_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '实际结算金额',
  `settlement_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '结算状态:0=未结算,1=已结算',
  `settlement_admin_id` int(11) NULL DEFAULT NULL COMMENT '结算操作员ID',
  `user_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户备注',
  `admin_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '管理员备注',
  `reject_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拒绝原因（用户不接受质检价格）',
  `return_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退回原因',
  `pickup_time_actual` int(11) NULL DEFAULT NULL COMMENT '实际取件时间（状态1→2）',
  `receive_time` int(11) NULL DEFAULT NULL COMMENT '平台收货时间（状态2→3）',
  `quality_start_time` int(11) NULL DEFAULT NULL COMMENT '质检开始时间（状态3→4）',
  `quality_complete_time` int(11) NULL DEFAULT NULL COMMENT '质检完成时间（状态4完成）',
  `confirm_time` int(11) NULL DEFAULT NULL COMMENT '用户确认时间（状态4→7或4→5）',
  `return_time` int(11) NULL DEFAULT NULL COMMENT '商品退回时间（状态5→6）',
  `return_express_company` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '退回快递公司',
  `return_express_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '退回快递单号',
  `return_note` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '退回备注',
  `settlement_time` int(11) NULL DEFAULT NULL COMMENT '结算完成时间（状态7）',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_no`(`order_no`) USING BTREE,
  INDEX `idx_quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `idx_member_id`(`member_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_source_type`(`source_type`) USING BTREE,
  INDEX `idx_delivery_type`(`delivery_type`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_member_status_time`(`member_id`, `status`, `create_time`) USING BTREE,
  INDEX `idx_pickup_address_id`(`pickup_address_id`) USING BTREE,
  INDEX `idx_voucher_id`(`voucher_id`) USING BTREE,
  INDEX `idx_express_status`(`express_status`) USING BTREE,
  INDEX `idx_return_express_no`(`return_express_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 76 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收订单主表' ROW_FORMAT = DYNAMIC [ RunTime:0.033888s ]
[2025-08-05T20:28:56+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_recycle_standards` [ RunTime:0.000449s ]
[2025-08-05T20:28:56+08:00][sql] CREATE TABLE `www_yz_she_recycle_standards`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '标准ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID（关联商品分类）',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标准项标题（如：外观、轻度磨损等）',
  `image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '示例图片',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '详细描述',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态：1启用 0禁用',
  `create_time` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_status_sort`(`category_id`, `status`, `sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收标准表' ROW_FORMAT = Dynamic [ RunTime:0.014072s ]
[2025-08-05T20:28:56+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_system_config` [ RunTime:0.000402s ]
[2025-08-05T20:28:56+08:00][sql] CREATE TABLE `www_yz_she_system_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `store_receiver_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货人姓名',
  `store_receiver_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货人电话',
  `store_province_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '省份名称',
  `store_city_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '城市名称',
  `store_district_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '区县名称',
  `store_address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '详细地址',
  `service_qr_code` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客服二维码',
  `service_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客服电话',
  `yunyang_appid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '云洋快递AppID',
  `yunyang_key` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '云洋快递Key',
  `yunyang_api_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '回调API地址',
  `yunyang_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否自动发单 0:禁用 1:启用',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '奢侈品系统配置表' ROW_FORMAT = Dynamic [ RunTime:0.011746s ]
[2025-08-05T20:28:56+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_voucher` [ RunTime:0.000337s ]
[2025-08-05T20:28:56+08:00][sql] CREATE TABLE `www_yz_she_voucher`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '加价券ID',
  `template_id` int(11) NOT NULL DEFAULT 0 COMMENT '模板ID',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `voucher_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '加价券编号',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '加价券标题',
  `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '类型：1-固定金额 2-百分比',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额或百分比',
  `min_condition_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最低使用金额',
  `max_discount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最大优惠金额',
  `applicable_goods_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '适用商品类型',
  `applicable_goods_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用商品ID',
  `applicable_category_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用分类ID',
  `applicable_brand_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用品牌ID',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-未使用 2-已使用 3-已过期',
  `receive_time` int(11) NOT NULL DEFAULT 0 COMMENT '发放时间',
  `use_time` int(11) NOT NULL DEFAULT 0 COMMENT '使用时间',
  `expire_time` int(11) NOT NULL DEFAULT 0 COMMENT '过期时间',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '使用的订单ID',
  `send_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '发放类型：1-注册自动发放 2-手动发放',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_voucher_no`(`voucher_no`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE,
  INDEX `idx_member_id`(`member_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_expire_time`(`expire_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 32 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户加价券表' ROW_FORMAT = Dynamic [ RunTime:0.017280s ]
[2025-08-05T20:28:56+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_voucher_send_log` [ RunTime:0.000325s ]
[2025-08-05T20:28:56+08:00][sql] CREATE TABLE `www_yz_she_voucher_send_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `template_id` int(11) NOT NULL DEFAULT 0 COMMENT '模板ID',
  `send_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '发放类型：1-注册自动发放 2-全部用户发放 3-指定用户发放',
  `send_count` int(11) NOT NULL DEFAULT 0 COMMENT '发放数量',
  `member_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '指定用户ID，JSON格式',
  `admin_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作管理员ID',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE,
  INDEX `idx_send_type`(`send_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '加价券发放记录表' ROW_FORMAT = Dynamic [ RunTime:0.013394s ]
[2025-08-05T20:28:56+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_voucher_template` [ RunTime:0.000692s ]
[2025-08-05T20:28:56+08:00][sql] CREATE TABLE `www_yz_she_voucher_template`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '加价券标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '加价券描述',
  `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '类型：1-固定金额 2-百分比',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额或百分比',
  `min_condition_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最低使用金额',
  `max_discount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最大优惠金额(百分比类型时使用)',
  `sum_count` int(11) NOT NULL DEFAULT -1 COMMENT '发放总数量，-1为不限制',
  `receive_count` int(11) NOT NULL DEFAULT 0 COMMENT '已发放数量',
  `use_count` int(11) NOT NULL DEFAULT 0 COMMENT '已使用数量',
  `valid_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '有效期类型：1-领取后N天有效 2-固定时间段',
  `length` int(11) NOT NULL DEFAULT 30 COMMENT '有效天数',
  `valid_start_time` int(11) NOT NULL DEFAULT 0 COMMENT '有效期开始时间',
  `valid_end_time` int(11) NOT NULL DEFAULT 0 COMMENT '有效期结束时间',
  `applicable_goods_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '适用商品类型：1-全部商品 2-指定商品 3-指定分类 4-指定品牌',
  `applicable_goods_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用商品ID，JSON格式',
  `applicable_category_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用分类ID，JSON格式',
  `applicable_brand_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用品牌ID，JSON格式',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：0-未开始 1-进行中 2-已结束 3-已关闭',
  `auto_send_register` tinyint(4) NOT NULL DEFAULT 0 COMMENT '新用户注册自动发放：0-否 1-是',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '加价券模板表' ROW_FORMAT = Dynamic [ RunTime:0.011859s ]
[2025-08-05T20:28:56+08:00][sql] DROP TRIGGER IF EXISTS `tr_quote_orders_auto_cancel_time` [ RunTime:0.000638s ]
[2025-08-05T20:28:56+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000398s ]
[2025-08-05T20:28:56+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000356s ]
[2025-08-05T20:28:56+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000280s ]
[2025-08-05T20:28:56+08:00][error] DEBUG：>>>>>>>>>
服务主体：0
IP：127.0.0.1
耗时（毫秒）：590
请求类型：POST
应用：adminapi
路由：/adminapi/addon/install/yz_she
请求参数：{"addon":"yz_she"}
错误信息：{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\service\\core\\addon\\CoreAddonInstallService.php","line":245,"message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'delimiter' at line 1","trace":[{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\service\\admin\\addon\\AddonService.php","line":60,"function":"install","class":"app\\service\\core\\addon\\CoreAddonInstallService","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\controller\\addon\\Addon.php","line":56,"function":"install","class":"app\\service\\admin\\addon\\AddonService","type":"->"},{"function":"install","class":"app\\adminapi\\controller\\addon\\Addon","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Container.php","line":345,"function":"invokeArgs","class":"ReflectionMethod","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":110,"function":"invokeReflectMethod","class":"think\\Container","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\route\\dispatch\\{closure}","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":113,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\Dispatch.php","line":52,"function":"exec","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":755,"function":"run","class":"think\\route\\Dispatch","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminLog.php","line":58,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminLog","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckRole.php","line":31,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckRole","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckToken.php","line":32,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckToken","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":756,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":208,"function":"dispatch","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":198,"function":"dispatchToRoute","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\app\\{closure}","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AllowCrossDomain.php","line":36,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AllowCrossDomain","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":72,"function":"then","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-trace\\src\\TraceDebug.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\trace\\TraceDebug","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":199,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":162,"function":"runWithRequest","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\public\\index.php","line":24,"function":"run","class":"think\\Http","type":"->"}],"previous":null}
---------
[2025-08-05T20:30:49+08:00][sql] CONNECT:[ UseTime:0.002034s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:30:49+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.001318s ]
[2025-08-05T20:30:49+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000238s ]
[2025-08-05T20:30:49+08:00][sql] CONNECT:[ UseTime:0.000959s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:30:49+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000580s ]
[2025-08-05T20:30:49+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000619s ]
[2025-08-05T20:30:50+08:00][sql] CONNECT:[ UseTime:0.013462s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:30:50+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000889s ]
[2025-08-05T20:30:50+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000798s ]
[2025-08-05T20:30:50+08:00][sql] CONNECT:[ UseTime:0.000865s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:30:50+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000557s ]
[2025-08-05T20:30:50+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000449s ]
[2025-08-05T20:30:50+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000499s ]
[2025-08-05T20:30:50+08:00][sql] CONNECT:[ UseTime:0.000929s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:30:50+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000611s ]
[2025-08-05T20:30:50+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.003258s ]
[2025-08-05T20:30:50+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000711s ]
[2025-08-05T20:30:50+08:00][sql] CONNECT:[ UseTime:0.000932s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:30:50+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000624s ]
[2025-08-05T20:30:50+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000634s ]
[2025-08-05T20:30:51+08:00][sql] CONNECT:[ UseTime:0.000888s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:30:51+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000859s ]
[2025-08-05T20:30:51+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000649s ]
[2025-08-05T20:30:51+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000599s ]
[2025-08-05T20:30:51+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`author`,`version`,`install_time`,`update_time`,`cover` FROM `www_addon` [ RunTime:0.000188s ]
[2025-08-05T20:30:51+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000423s ]
[2025-08-05T20:30:52+08:00][sql] CONNECT:[ UseTime:0.001126s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:30:52+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000761s ]
[2025-08-05T20:30:52+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000834s ]
[2025-08-05T20:30:52+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000758s ]
[2025-08-05T20:30:52+08:00][error] DEBUG：>>>>>>>>>
服务主体：0
IP：127.0.0.1
耗时（毫秒）：285
请求类型：GET
应用：adminapi
路由：/adminapi/niucloud/authinfo
请求参数：[]
错误信息：{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":148,"message":"\u6388\u6743\u7801\u6216\u6388\u6743\u79d8\u94a5\u9519\u8bef","trace":[{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\guzzle\\src\\RetryMiddleware.php","line":84,"function":"core\\util\\niucloud\\{closure}","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\FulfilledPromise.php","line":41,"function":"GuzzleHttp\\{closure}","class":"GuzzleHttp\\RetryMiddleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php","line":48,"function":"GuzzleHttp\\Promise\\{closure}","class":"GuzzleHttp\\Promise\\FulfilledPromise","type":"::"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":248,"function":"run","class":"GuzzleHttp\\Promise\\TaskQueue","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":224,"function":"invokeWaitFn","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":269,"function":"waitIfPending","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":226,"function":"invokeWaitList","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":62,"function":"waitIfPending","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\guzzle\\src\\Client.php","line":187,"function":"wait","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\http\\HasHttpRequests.php","line":97,"function":"request","class":"GuzzleHttp\\Client","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":110,"function":"toRequest","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":193,"function":"request","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\service\\core\\niucloud\\CoreAuthService.php","line":31,"function":"httpGet","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\controller\\niucloud\\Module.php","line":35,"function":"getAuthInfo","class":"app\\service\\core\\niucloud\\CoreAuthService","type":"->"},{"function":"authorize","class":"app\\adminapi\\controller\\niucloud\\Module","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Container.php","line":345,"function":"invokeArgs","class":"ReflectionMethod","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":110,"function":"invokeReflectMethod","class":"think\\Container","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\route\\dispatch\\{closure}","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":113,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\Dispatch.php","line":52,"function":"exec","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":755,"function":"run","class":"think\\route\\Dispatch","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminLog.php","line":58,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminLog","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckRole.php","line":31,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckRole","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckToken.php","line":32,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckToken","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":756,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":208,"function":"dispatch","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":198,"function":"dispatchToRoute","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\app\\{closure}","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AllowCrossDomain.php","line":36,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AllowCrossDomain","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":72,"function":"then","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-trace\\src\\TraceDebug.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\trace\\TraceDebug","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":199,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":162,"function":"runWithRequest","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\public\\index.php","line":24,"function":"run","class":"think\\Http","type":"->"}],"previous":null}
---------
[2025-08-05T20:30:52+08:00][sql] CONNECT:[ UseTime:0.009875s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:30:52+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000759s ]
[2025-08-05T20:30:52+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000709s ]
[2025-08-05T20:30:53+08:00][sql] CONNECT:[ UseTime:0.010987s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:30:53+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001974s ]
[2025-08-05T20:30:53+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001615s ]
[2025-08-05T20:30:53+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.001387s ]
[2025-08-05T20:30:53+08:00][sql] CONNECT:[ UseTime:0.010428s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:30:53+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000636s ]
[2025-08-05T20:30:53+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000582s ]
[2025-08-05T20:30:53+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000746s ]
[2025-08-05T20:30:53+08:00][sql] CONNECT:[ UseTime:0.010245s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:30:53+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.009865s ]
[2025-08-05T20:30:53+08:00][sql] CONNECT:[ UseTime:0.011786s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:30:53+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.001505s ]
[2025-08-05T20:30:53+08:00][sql] CONNECT:[ UseTime:0.011178s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:30:53+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000597s ]
[2025-08-05T20:30:53+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000979s ]
[2025-08-05T20:30:53+08:00][sql] CONNECT:[ UseTime:0.000990s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:30:53+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000564s ]
[2025-08-05T20:30:53+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000628s ]
[2025-08-05T20:30:53+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000701s ]
[2025-08-05T20:30:53+08:00][error] DEBUG：>>>>>>>>>
服务主体：0
IP：127.0.0.1
耗时（毫秒）：251
请求类型：GET
应用：adminapi
路由：/adminapi/niucloud/authinfo
请求参数：[]
错误信息：{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":148,"message":"\u6388\u6743\u7801\u6216\u6388\u6743\u79d8\u94a5\u9519\u8bef","trace":[{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\guzzle\\src\\RetryMiddleware.php","line":84,"function":"core\\util\\niucloud\\{closure}","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\FulfilledPromise.php","line":41,"function":"GuzzleHttp\\{closure}","class":"GuzzleHttp\\RetryMiddleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php","line":48,"function":"GuzzleHttp\\Promise\\{closure}","class":"GuzzleHttp\\Promise\\FulfilledPromise","type":"::"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":248,"function":"run","class":"GuzzleHttp\\Promise\\TaskQueue","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":224,"function":"invokeWaitFn","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":269,"function":"waitIfPending","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":226,"function":"invokeWaitList","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":62,"function":"waitIfPending","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\guzzle\\src\\Client.php","line":187,"function":"wait","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\http\\HasHttpRequests.php","line":97,"function":"request","class":"GuzzleHttp\\Client","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":110,"function":"toRequest","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":193,"function":"request","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\service\\core\\niucloud\\CoreAuthService.php","line":31,"function":"httpGet","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\controller\\niucloud\\Module.php","line":35,"function":"getAuthInfo","class":"app\\service\\core\\niucloud\\CoreAuthService","type":"->"},{"function":"authorize","class":"app\\adminapi\\controller\\niucloud\\Module","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Container.php","line":345,"function":"invokeArgs","class":"ReflectionMethod","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":110,"function":"invokeReflectMethod","class":"think\\Container","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\route\\dispatch\\{closure}","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":113,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\Dispatch.php","line":52,"function":"exec","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":755,"function":"run","class":"think\\route\\Dispatch","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminLog.php","line":58,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminLog","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckRole.php","line":31,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckRole","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckToken.php","line":32,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckToken","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":756,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":208,"function":"dispatch","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":198,"function":"dispatchToRoute","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\app\\{closure}","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AllowCrossDomain.php","line":36,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AllowCrossDomain","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":72,"function":"then","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-trace\\src\\TraceDebug.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\trace\\TraceDebug","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":199,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":162,"function":"runWithRequest","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\public\\index.php","line":24,"function":"run","class":"think\\Http","type":"->"}],"previous":null}
---------
[2025-08-05T20:30:54+08:00][sql] CONNECT:[ UseTime:0.011707s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:30:54+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.002138s ]
[2025-08-05T20:30:54+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001176s ]
[2025-08-05T20:30:54+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.001352s ]
[2025-08-05T20:30:55+08:00][sql] CONNECT:[ UseTime:0.010430s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:30:55+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000606s ]
[2025-08-05T20:30:55+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000644s ]
[2025-08-05T20:30:55+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user_log` [ RunTime:0.004104s ]
[2025-08-05T20:30:55+08:00][sql] INSERT INTO `www_sys_user_log` SET `uid` = 1 , `username` = 'admin' , `url` = '/adminapi/addon/install/yz_she' , `params` = '{\"addon\":\"yz_she\"}' , `ip` = '127.0.0.1' , `type` = 'POST' , `operation` = '插件管理-安装插件' , `create_time` = 1754397055 [ RunTime:0.000392s ]
[2025-08-05T20:30:55+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000646s ]
[2025-08-05T20:30:55+08:00][sql] SELECT * FROM `www_addon` WHERE  `key` = 'yz_she' LIMIT 1 [ RunTime:0.000372s ]
[2025-08-05T20:30:55+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000437s ]
[2025-08-05T20:30:55+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000342s ]
[2025-08-05T20:30:55+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000335s ]
[2025-08-05T20:30:55+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_brands` [ RunTime:0.000391s ]
[2025-08-05T20:30:55+08:00][sql] CREATE TABLE `www_yz_she_brands`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '品牌ID',
  `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '品牌名称',
  `logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '品牌Logo',
  `letter` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'A' COMMENT '首字母索引',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '是否热门品牌 1是 0否',
  `hot_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '热门品牌名字',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1启用 0禁用',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_letter`(`letter`) USING BTREE,
  INDEX `idx_is_hot`(`is_hot`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '品牌表' ROW_FORMAT = Dynamic [ RunTime:0.022608s ]
[2025-08-05T20:30:55+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_categories` [ RunTime:0.001045s ]
[2025-08-05T20:30:55+08:00][sql] CREATE TABLE `www_yz_she_categories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类图标',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类图片',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1启用 0禁用',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收分类表' ROW_FORMAT = Dynamic [ RunTime:0.023976s ]
[2025-08-05T20:30:55+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_category_accessories` [ RunTime:0.000779s ]
[2025-08-05T20:30:55+08:00][sql] CREATE TABLE `www_yz_she_category_accessories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `accessory_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配件名称',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分类配件配置表' ROW_FORMAT = Dynamic [ RunTime:0.017514s ]
[2025-08-05T20:30:55+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_category_photos` [ RunTime:0.000508s ]
[2025-08-05T20:30:55+08:00][sql] CREATE TABLE `www_yz_she_category_photos`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `photo_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片名称',
  `background_image` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'upload/background/default-photo-bg.png' COMMENT '背景图片',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分类照片配置表' ROW_FORMAT = Dynamic [ RunTime:0.014612s ]
[2025-08-05T20:30:55+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_express_log` [ RunTime:0.000474s ]
[2025-08-05T20:30:55+08:00][sql] CREATE TABLE `www_yz_she_express_log`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint(20) UNSIGNED NOT NULL COMMENT '订单ID',
  `waybill` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运单号',
  `shopbill` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家单号',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '运单状态描述',
  `type_code` tinyint(1) NULL DEFAULT NULL COMMENT '状态码：1待揽收，2运输中，3已签收，4拒收退回，99已取消',
  `weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '下单重量',
  `real_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '站点称重',
  `transfer_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '分拣称重',
  `cal_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '计费重量',
  `volume` decimal(10, 2) NULL DEFAULT NULL COMMENT '体积',
  `parse_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '体积换算重量',
  `total_freight` decimal(10, 2) NULL DEFAULT NULL COMMENT '运单总扣款费用',
  `freight` decimal(10, 2) NULL DEFAULT NULL COMMENT '快递费',
  `freight_insured` decimal(10, 2) NULL DEFAULT NULL COMMENT '保价费',
  `freight_haocai` decimal(10, 2) NULL DEFAULT NULL COMMENT '增值费用',
  `change_bill` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '换单号',
  `change_bill_freight` decimal(10, 2) NULL DEFAULT NULL COMMENT '逆向费',
  `fee_over` tinyint(1) NULL DEFAULT NULL COMMENT '订单扣费状态：1已扣费，0冻结',
  `courier_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递员姓名',
  `courier_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递员电话',
  `pickup_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '取件码',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '回调原始内容',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_waybill`(`waybill`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '物流回调日志表' ROW_FORMAT = DYNAMIC [ RunTime:0.020215s ]
[2025-08-05T20:30:55+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_goods` [ RunTime:0.000478s ]
[2025-08-05T20:30:55+08:00][sql] CREATE TABLE `www_yz_she_goods`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `brand_id` int(11) NOT NULL DEFAULT 0 COMMENT '品牌ID',
  `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品货号/型号',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品图片JSON格式',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品描述',
  `price_new` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '全新品回收价格',
  `price_used` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '二手品回收价格',
  `price_damaged` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '有瑕疵回收价格',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '是否热门商品 1是 0否',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1上架 0下架',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_brand_id`(`brand_id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_code`(`code`) USING BTREE,
  INDEX `idx_is_hot`(`is_hot`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品表' ROW_FORMAT = Dynamic [ RunTime:0.021776s ]
[2025-08-05T20:30:55+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_quote_orders` [ RunTime:0.000382s ]
[2025-08-05T20:30:55+08:00][sql] CREATE TABLE `www_yz_she_quote_orders`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `brand_id` int(11) NULL DEFAULT NULL COMMENT '品牌ID',
  `product_id` int(11) NULL DEFAULT NULL COMMENT '商品ID',
  `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `product_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品编码',
  `product_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品图片/品牌图片',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单状态:1=估价中,2=待确认,3=待发货,4=已完成,5=已取消',
  `quote_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '估价金额',
  `user_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户备注',
  `admin_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '管理员备注',
  `admin_id` int(11) NULL DEFAULT NULL COMMENT '估价管理员ID',
  `quote_time` datetime NULL DEFAULT NULL COMMENT '估价时间',
  `confirm_time` datetime NULL DEFAULT NULL COMMENT '用户确认时间',
  `ship_time` datetime NULL DEFAULT NULL COMMENT '用户发货时间',
  `complete_time` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `cancel_time` datetime NULL DEFAULT NULL COMMENT '取消时间',
  `cancel_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '取消原因',
  `auto_cancel_time` datetime NULL DEFAULT NULL COMMENT '自动取消时间(确认后48小时)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_no`(`order_no`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  INDEX `brand_id`(`brand_id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `admin_id`(`admin_id`) USING BTREE,
  INDEX `create_time`(`create_time`) USING BTREE,
  INDEX `auto_cancel_time`(`auto_cancel_time`) USING BTREE,
  INDEX `idx_orders_user_status_time`(`user_id`, `status`, `create_time`) USING BTREE,
  INDEX `idx_orders_status_time`(`status`, `create_time`) USING BTREE,
  INDEX `idx_orders_auto_cancel`(`status`, `auto_cancel_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 37 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价订单主表' ROW_FORMAT = Dynamic [ RunTime:0.028715s ]
[2025-08-05T20:30:55+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_order_status_logs` [ RunTime:0.000368s ]
[2025-08-05T20:30:55+08:00][sql] CREATE TABLE `www_yz_she_order_status_logs`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `from_status` tinyint(1) NULL DEFAULT NULL COMMENT '原状态',
  `to_status` tinyint(1) NOT NULL COMMENT '新状态',
  `operator_id` int(11) NULL DEFAULT NULL COMMENT '操作人ID',
  `operator_type` tinyint(1) NOT NULL COMMENT '操作人类型:1=用户,2=管理员,3=系统',
  `change_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更原因',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `operator_id`(`operator_id`) USING BTREE,
  INDEX `create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价订单状态变更日志表' ROW_FORMAT = Dynamic [ RunTime:0.016705s ]
[2025-08-05T20:30:55+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_quote_accessories` [ RunTime:0.000396s ]
[2025-08-05T20:30:55+08:00][sql] CREATE TABLE `www_yz_she_quote_accessories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配件ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `accessory_config_id` int(11) NULL DEFAULT NULL COMMENT '配件配置ID',
  `accessory_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配件名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `accessory_config_id`(`accessory_config_id`) USING BTREE,
  INDEX `idx_accessories_order`(`quote_order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价订单配件表' ROW_FORMAT = Dynamic [ RunTime:0.014349s ]
[2025-08-05T20:30:55+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_quote_photos` [ RunTime:0.000678s ]
[2025-08-05T20:30:55+08:00][sql] CREATE TABLE `www_yz_she_quote_photos`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '照片ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `photo_config_id` int(11) NULL DEFAULT NULL COMMENT '照片配置ID',
  `photo_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片类型',
  `photo_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片名称',
  `photo_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片URL',
  `is_defect` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为瑕疵照片:0=否,1=是',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `photo_config_id`(`photo_config_id`) USING BTREE,
  INDEX `photo_type`(`photo_type`) USING BTREE,
  INDEX `is_defect`(`is_defect`) USING BTREE,
  INDEX `idx_photos_order_type`(`quote_order_id`, `photo_type`, `sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价订单照片表' ROW_FORMAT = Dynamic [ RunTime:0.017965s ]
[2025-08-05T20:30:55+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_quote_records` [ RunTime:0.000314s ]
[2025-08-05T20:30:55+08:00][sql] CREATE TABLE `www_yz_she_quote_records`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `admin_id` int(11) NOT NULL COMMENT '估价管理员ID',
  `condition_score` int(3) NULL DEFAULT NULL COMMENT '成色评分(0-100)',
  `quote_price` decimal(10, 2) NOT NULL COMMENT '估价金额',
  `quote_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '估价说明',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `admin_id`(`admin_id`) USING BTREE,
  INDEX `create_time`(`create_time`) USING BTREE,
  INDEX `idx_records_order_time`(`quote_order_id`, `create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价记录表' ROW_FORMAT = Dynamic [ RunTime:0.016037s ]
[2025-08-05T20:30:55+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_recycle_order_logs` [ RunTime:0.000326s ]
[2025-08-05T20:30:55+08:00][sql] CREATE TABLE `www_yz_she_recycle_order_logs`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `recycle_order_id` int(11) NOT NULL COMMENT '回收订单ID',
  `from_status` tinyint(1) NULL DEFAULT NULL COMMENT '原状态',
  `to_status` tinyint(1) NOT NULL COMMENT '新状态',
  `operator_id` int(11) NULL DEFAULT NULL COMMENT '操作人ID',
  `operator_type` tinyint(1) NOT NULL COMMENT '操作人类型:1=用户,2=管理员,3=系统',
  `change_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更原因',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `extra_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '额外数据JSON',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_recycle_order_id`(`recycle_order_id`) USING BTREE,
  INDEX `idx_operator_id`(`operator_id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 315 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收订单状态变更日志表' ROW_FORMAT = DYNAMIC [ RunTime:0.015266s ]
[2025-08-05T20:30:55+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_recycle_orders` [ RunTime:0.000383s ]
[2025-08-05T20:30:55+08:00][sql] CREATE TABLE `www_yz_she_recycle_orders`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '回收订单ID',
  `order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '回收订单编号',
  `quote_order_id` int(11) NULL DEFAULT NULL COMMENT '关联的估价订单ID（从估价订单创建时有值）',
  `member_id` int(11) NOT NULL COMMENT '会员ID（对应member表）',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `brand_id` int(11) NULL DEFAULT NULL COMMENT '品牌ID',
  `product_id` int(11) NULL DEFAULT NULL COMMENT '商品ID',
  `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `product_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品编码',
  `product_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品图片',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单状态:1=待取件,2=待收货,3=待质检,4=待确认,5=待退回,6=已退回,7=已完成,8=已取消',
  `source_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单来源:1=估价订单确认,2=直接回收,3=批量下单',
  `quantity` int(11) NOT NULL DEFAULT 1 COMMENT '回收数量',
  `expected_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '预期回收价格（用户选择的价格）',
  `final_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '最终回收价格（质检后确定）',
  `voucher_id` int(11) NULL DEFAULT NULL COMMENT '使用的加价券ID（对应www_yz_she_voucher表）',
  `voucher_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '加价券金额',
  `total_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '最终结算金额',
  `delivery_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '配送方式:1=快递上门,2=自行寄出',
  `pickup_address_id` int(11) NULL DEFAULT NULL COMMENT '上门取件地址ID（对应www_member_address表）',
  `pickup_time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '期望上门时间',
  `pickup_contact_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系人姓名',
  `pickup_contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系人电话',
  `pickup_address_detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '详细地址信息',
  `express_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递回调ID',
  `express_company` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递公司',
  `express_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递单号',
  `express_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '快递费用',
  `express_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '快递状态:0=暂无轨迹,1=已揽收,2=运输中,3=已签收,4=异常',
  `quality_check_admin_id` int(11) NULL DEFAULT NULL COMMENT '质检员ID',
  `quality_score` int(3) NULL DEFAULT NULL COMMENT '质检评分(0-100)',
  `quality_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '质检说明',
  `quality_images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '质检照片JSON',
  `settlement_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '结算方式:1=余额结算',
  `settlement_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '实际结算金额',
  `settlement_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '结算状态:0=未结算,1=已结算',
  `settlement_admin_id` int(11) NULL DEFAULT NULL COMMENT '结算操作员ID',
  `user_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户备注',
  `admin_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '管理员备注',
  `reject_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拒绝原因（用户不接受质检价格）',
  `return_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退回原因',
  `pickup_time_actual` int(11) NULL DEFAULT NULL COMMENT '实际取件时间（状态1→2）',
  `receive_time` int(11) NULL DEFAULT NULL COMMENT '平台收货时间（状态2→3）',
  `quality_start_time` int(11) NULL DEFAULT NULL COMMENT '质检开始时间（状态3→4）',
  `quality_complete_time` int(11) NULL DEFAULT NULL COMMENT '质检完成时间（状态4完成）',
  `confirm_time` int(11) NULL DEFAULT NULL COMMENT '用户确认时间（状态4→7或4→5）',
  `return_time` int(11) NULL DEFAULT NULL COMMENT '商品退回时间（状态5→6）',
  `return_express_company` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '退回快递公司',
  `return_express_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '退回快递单号',
  `return_note` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '退回备注',
  `settlement_time` int(11) NULL DEFAULT NULL COMMENT '结算完成时间（状态7）',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_no`(`order_no`) USING BTREE,
  INDEX `idx_quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `idx_member_id`(`member_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_source_type`(`source_type`) USING BTREE,
  INDEX `idx_delivery_type`(`delivery_type`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_member_status_time`(`member_id`, `status`, `create_time`) USING BTREE,
  INDEX `idx_pickup_address_id`(`pickup_address_id`) USING BTREE,
  INDEX `idx_voucher_id`(`voucher_id`) USING BTREE,
  INDEX `idx_express_status`(`express_status`) USING BTREE,
  INDEX `idx_return_express_no`(`return_express_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 76 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收订单主表' ROW_FORMAT = DYNAMIC [ RunTime:0.027542s ]
[2025-08-05T20:30:55+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_recycle_standards` [ RunTime:0.000416s ]
[2025-08-05T20:30:55+08:00][sql] CREATE TABLE `www_yz_she_recycle_standards`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '标准ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID（关联商品分类）',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标准项标题（如：外观、轻度磨损等）',
  `image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '示例图片',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '详细描述',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态：1启用 0禁用',
  `create_time` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_status_sort`(`category_id`, `status`, `sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收标准表' ROW_FORMAT = Dynamic [ RunTime:0.012141s ]
[2025-08-05T20:30:55+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_system_config` [ RunTime:0.000442s ]
[2025-08-05T20:30:55+08:00][sql] CREATE TABLE `www_yz_she_system_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `store_receiver_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货人姓名',
  `store_receiver_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货人电话',
  `store_province_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '省份名称',
  `store_city_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '城市名称',
  `store_district_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '区县名称',
  `store_address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '详细地址',
  `service_qr_code` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客服二维码',
  `service_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客服电话',
  `yunyang_appid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '云洋快递AppID',
  `yunyang_key` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '云洋快递Key',
  `yunyang_api_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '回调API地址',
  `yunyang_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否自动发单 0:禁用 1:启用',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '奢侈品系统配置表' ROW_FORMAT = Dynamic [ RunTime:0.010956s ]
[2025-08-05T20:30:55+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_voucher` [ RunTime:0.000364s ]
[2025-08-05T20:30:55+08:00][sql] CREATE TABLE `www_yz_she_voucher`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '加价券ID',
  `template_id` int(11) NOT NULL DEFAULT 0 COMMENT '模板ID',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `voucher_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '加价券编号',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '加价券标题',
  `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '类型：1-固定金额 2-百分比',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额或百分比',
  `min_condition_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最低使用金额',
  `max_discount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最大优惠金额',
  `applicable_goods_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '适用商品类型',
  `applicable_goods_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用商品ID',
  `applicable_category_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用分类ID',
  `applicable_brand_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用品牌ID',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-未使用 2-已使用 3-已过期',
  `receive_time` int(11) NOT NULL DEFAULT 0 COMMENT '发放时间',
  `use_time` int(11) NOT NULL DEFAULT 0 COMMENT '使用时间',
  `expire_time` int(11) NOT NULL DEFAULT 0 COMMENT '过期时间',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '使用的订单ID',
  `send_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '发放类型：1-注册自动发放 2-手动发放',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_voucher_no`(`voucher_no`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE,
  INDEX `idx_member_id`(`member_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_expire_time`(`expire_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 32 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户加价券表' ROW_FORMAT = Dynamic [ RunTime:0.016383s ]
[2025-08-05T20:30:55+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_voucher_send_log` [ RunTime:0.000286s ]
[2025-08-05T20:30:55+08:00][sql] CREATE TABLE `www_yz_she_voucher_send_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `template_id` int(11) NOT NULL DEFAULT 0 COMMENT '模板ID',
  `send_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '发放类型：1-注册自动发放 2-全部用户发放 3-指定用户发放',
  `send_count` int(11) NOT NULL DEFAULT 0 COMMENT '发放数量',
  `member_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '指定用户ID，JSON格式',
  `admin_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作管理员ID',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE,
  INDEX `idx_send_type`(`send_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '加价券发放记录表' ROW_FORMAT = Dynamic [ RunTime:0.012566s ]
[2025-08-05T20:30:55+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_voucher_template` [ RunTime:0.000327s ]
[2025-08-05T20:30:55+08:00][sql] CREATE TABLE `www_yz_she_voucher_template`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '加价券标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '加价券描述',
  `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '类型：1-固定金额 2-百分比',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额或百分比',
  `min_condition_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最低使用金额',
  `max_discount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最大优惠金额(百分比类型时使用)',
  `sum_count` int(11) NOT NULL DEFAULT -1 COMMENT '发放总数量，-1为不限制',
  `receive_count` int(11) NOT NULL DEFAULT 0 COMMENT '已发放数量',
  `use_count` int(11) NOT NULL DEFAULT 0 COMMENT '已使用数量',
  `valid_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '有效期类型：1-领取后N天有效 2-固定时间段',
  `length` int(11) NOT NULL DEFAULT 30 COMMENT '有效天数',
  `valid_start_time` int(11) NOT NULL DEFAULT 0 COMMENT '有效期开始时间',
  `valid_end_time` int(11) NOT NULL DEFAULT 0 COMMENT '有效期结束时间',
  `applicable_goods_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '适用商品类型：1-全部商品 2-指定商品 3-指定分类 4-指定品牌',
  `applicable_goods_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用商品ID，JSON格式',
  `applicable_category_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用分类ID，JSON格式',
  `applicable_brand_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用品牌ID，JSON格式',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：0-未开始 1-进行中 2-已结束 3-已关闭',
  `auto_send_register` tinyint(4) NOT NULL DEFAULT 0 COMMENT '新用户注册自动发放：0-否 1-是',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '加价券模板表' ROW_FORMAT = Dynamic [ RunTime:0.011616s ]
[2025-08-05T20:30:55+08:00][sql] DROP TRIGGER IF EXISTS `tr_quote_orders_auto_cancel_time` [ RunTime:0.000218s ]
[2025-08-05T20:30:55+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000464s ]
[2025-08-05T20:30:55+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000511s ]
[2025-08-05T20:30:55+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000358s ]
[2025-08-05T20:30:55+08:00][error] DEBUG：>>>>>>>>>
服务主体：0
IP：127.0.0.1
耗时（毫秒）：554
请求类型：POST
应用：adminapi
路由：/adminapi/addon/install/yz_she
请求参数：{"addon":"yz_she"}
错误信息：{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\service\\core\\addon\\CoreAddonInstallService.php","line":245,"message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 5","trace":[{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\service\\admin\\addon\\AddonService.php","line":60,"function":"install","class":"app\\service\\core\\addon\\CoreAddonInstallService","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\controller\\addon\\Addon.php","line":56,"function":"install","class":"app\\service\\admin\\addon\\AddonService","type":"->"},{"function":"install","class":"app\\adminapi\\controller\\addon\\Addon","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Container.php","line":345,"function":"invokeArgs","class":"ReflectionMethod","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":110,"function":"invokeReflectMethod","class":"think\\Container","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\route\\dispatch\\{closure}","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":113,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\Dispatch.php","line":52,"function":"exec","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":755,"function":"run","class":"think\\route\\Dispatch","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminLog.php","line":58,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminLog","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckRole.php","line":31,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckRole","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckToken.php","line":32,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckToken","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":756,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":208,"function":"dispatch","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":198,"function":"dispatchToRoute","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\app\\{closure}","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AllowCrossDomain.php","line":36,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AllowCrossDomain","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":72,"function":"then","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-trace\\src\\TraceDebug.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\trace\\TraceDebug","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":199,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":162,"function":"runWithRequest","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\public\\index.php","line":24,"function":"run","class":"think\\Http","type":"->"}],"previous":null}
---------
[2025-08-05T20:33:12+08:00][sql] CONNECT:[ UseTime:0.001066s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:33:12+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.001154s ]
[2025-08-05T20:33:12+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000282s ]
[2025-08-05T20:33:12+08:00][sql] CONNECT:[ UseTime:0.000880s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:33:12+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000590s ]
[2025-08-05T20:33:12+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000960s ]
[2025-08-05T20:33:13+08:00][sql] CONNECT:[ UseTime:0.000880s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:33:13+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000846s ]
[2025-08-05T20:33:13+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001257s ]
[2025-08-05T20:33:13+08:00][sql] CONNECT:[ UseTime:0.000949s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:33:13+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000650s ]
[2025-08-05T20:33:13+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000646s ]
[2025-08-05T20:33:13+08:00][sql] CONNECT:[ UseTime:0.000833s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:33:13+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000572s ]
[2025-08-05T20:33:13+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000631s ]
[2025-08-05T20:33:13+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000677s ]
[2025-08-05T20:33:14+08:00][sql] CONNECT:[ UseTime:0.011152s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:33:14+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000677s ]
[2025-08-05T20:33:14+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000706s ]
[2025-08-05T20:33:14+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000717s ]
[2025-08-05T20:33:14+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`author`,`version`,`install_time`,`update_time`,`cover` FROM `www_addon` [ RunTime:0.000272s ]
[2025-08-05T20:33:14+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000731s ]
[2025-08-05T20:33:15+08:00][sql] CONNECT:[ UseTime:0.011841s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:33:15+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001859s ]
[2025-08-05T20:33:15+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001237s ]
[2025-08-05T20:33:15+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.001064s ]
[2025-08-05T20:33:15+08:00][error] DEBUG：>>>>>>>>>
服务主体：0
IP：127.0.0.1
耗时（毫秒）：273
请求类型：GET
应用：adminapi
路由：/adminapi/niucloud/authinfo
请求参数：[]
错误信息：{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":148,"message":"\u6388\u6743\u7801\u6216\u6388\u6743\u79d8\u94a5\u9519\u8bef","trace":[{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\guzzle\\src\\RetryMiddleware.php","line":84,"function":"core\\util\\niucloud\\{closure}","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\FulfilledPromise.php","line":41,"function":"GuzzleHttp\\{closure}","class":"GuzzleHttp\\RetryMiddleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php","line":48,"function":"GuzzleHttp\\Promise\\{closure}","class":"GuzzleHttp\\Promise\\FulfilledPromise","type":"::"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":248,"function":"run","class":"GuzzleHttp\\Promise\\TaskQueue","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":224,"function":"invokeWaitFn","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":269,"function":"waitIfPending","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":226,"function":"invokeWaitList","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":62,"function":"waitIfPending","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\guzzle\\src\\Client.php","line":187,"function":"wait","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\http\\HasHttpRequests.php","line":97,"function":"request","class":"GuzzleHttp\\Client","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":110,"function":"toRequest","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":193,"function":"request","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\service\\core\\niucloud\\CoreAuthService.php","line":31,"function":"httpGet","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\controller\\niucloud\\Module.php","line":35,"function":"getAuthInfo","class":"app\\service\\core\\niucloud\\CoreAuthService","type":"->"},{"function":"authorize","class":"app\\adminapi\\controller\\niucloud\\Module","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Container.php","line":345,"function":"invokeArgs","class":"ReflectionMethod","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":110,"function":"invokeReflectMethod","class":"think\\Container","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\route\\dispatch\\{closure}","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":113,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\Dispatch.php","line":52,"function":"exec","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":755,"function":"run","class":"think\\route\\Dispatch","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminLog.php","line":58,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminLog","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckRole.php","line":31,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckRole","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckToken.php","line":32,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckToken","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":756,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":208,"function":"dispatch","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":198,"function":"dispatchToRoute","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\app\\{closure}","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AllowCrossDomain.php","line":36,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AllowCrossDomain","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":72,"function":"then","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-trace\\src\\TraceDebug.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\trace\\TraceDebug","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":199,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":162,"function":"runWithRequest","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\public\\index.php","line":24,"function":"run","class":"think\\Http","type":"->"}],"previous":null}
---------
[2025-08-05T20:33:15+08:00][sql] CONNECT:[ UseTime:0.010562s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:33:15+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000639s ]
[2025-08-05T20:33:15+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000675s ]
[2025-08-05T20:33:15+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.008953s ]
[2025-08-05T20:33:15+08:00][sql] CONNECT:[ UseTime:0.001102s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:33:15+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001100s ]
[2025-08-05T20:33:15+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000952s ]
[2025-08-05T20:33:16+08:00][sql] CONNECT:[ UseTime:0.011283s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:33:16+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.001136s ]
[2025-08-05T20:33:16+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000621s ]
[2025-08-05T20:33:16+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000520s ]
[2025-08-05T20:33:16+08:00][sql] CONNECT:[ UseTime:0.009849s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:33:16+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000870s ]
[2025-08-05T20:33:16+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000784s ]
[2025-08-05T20:33:16+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.001611s ]
[2025-08-05T20:33:16+08:00][sql] CONNECT:[ UseTime:0.001063s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:33:16+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.000857s ]
[2025-08-05T20:33:16+08:00][sql] CONNECT:[ UseTime:0.000948s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:33:16+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000976s ]
[2025-08-05T20:33:16+08:00][sql] CONNECT:[ UseTime:0.011553s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:33:16+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000591s ]
[2025-08-05T20:33:16+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000563s ]
[2025-08-05T20:33:17+08:00][sql] CONNECT:[ UseTime:0.011751s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:33:17+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.002889s ]
[2025-08-05T20:33:17+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001800s ]
[2025-08-05T20:33:17+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_config` [ RunTime:0.001392s ]
[2025-08-05T20:33:17+08:00][error] DEBUG：>>>>>>>>>
服务主体：0
IP：127.0.0.1
耗时（毫秒）：246
请求类型：GET
应用：adminapi
路由：/adminapi/niucloud/authinfo
请求参数：[]
错误信息：{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":148,"message":"\u6388\u6743\u7801\u6216\u6388\u6743\u79d8\u94a5\u9519\u8bef","trace":[{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\guzzle\\src\\RetryMiddleware.php","line":84,"function":"core\\util\\niucloud\\{closure}","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\FulfilledPromise.php","line":41,"function":"GuzzleHttp\\{closure}","class":"GuzzleHttp\\RetryMiddleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php","line":48,"function":"GuzzleHttp\\Promise\\{closure}","class":"GuzzleHttp\\Promise\\FulfilledPromise","type":"::"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":248,"function":"run","class":"GuzzleHttp\\Promise\\TaskQueue","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":224,"function":"invokeWaitFn","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":269,"function":"waitIfPending","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":226,"function":"invokeWaitList","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\promises\\src\\Promise.php","line":62,"function":"waitIfPending","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\guzzlehttp\\guzzle\\src\\Client.php","line":187,"function":"wait","class":"GuzzleHttp\\Promise\\Promise","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\http\\HasHttpRequests.php","line":97,"function":"request","class":"GuzzleHttp\\Client","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":110,"function":"toRequest","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\core\\util\\niucloud\\BaseNiucloudClient.php","line":193,"function":"request","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\service\\core\\niucloud\\CoreAuthService.php","line":31,"function":"httpGet","class":"core\\util\\niucloud\\BaseNiucloudClient","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\controller\\niucloud\\Module.php","line":35,"function":"getAuthInfo","class":"app\\service\\core\\niucloud\\CoreAuthService","type":"->"},{"function":"authorize","class":"app\\adminapi\\controller\\niucloud\\Module","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Container.php","line":345,"function":"invokeArgs","class":"ReflectionMethod","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":110,"function":"invokeReflectMethod","class":"think\\Container","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\route\\dispatch\\{closure}","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":113,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\Dispatch.php","line":52,"function":"exec","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":755,"function":"run","class":"think\\route\\Dispatch","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminLog.php","line":58,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminLog","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckRole.php","line":31,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckRole","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckToken.php","line":32,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckToken","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":756,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":208,"function":"dispatch","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":198,"function":"dispatchToRoute","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\app\\{closure}","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AllowCrossDomain.php","line":36,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AllowCrossDomain","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":72,"function":"then","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-trace\\src\\TraceDebug.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\trace\\TraceDebug","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":199,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":162,"function":"runWithRequest","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\public\\index.php","line":24,"function":"run","class":"think\\Http","type":"->"}],"previous":null}
---------
[2025-08-05T20:33:17+08:00][sql] CONNECT:[ UseTime:0.010102s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:33:17+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000635s ]
[2025-08-05T20:33:17+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.001043s ]
[2025-08-05T20:33:17+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000613s ]
[2025-08-05T20:33:18+08:00][sql] CONNECT:[ UseTime:0.011550s ] mysql:host=localhost;port=3306;dbname=yy_com;charset=utf8mb4
[2025-08-05T20:33:18+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user` [ RunTime:0.000662s ]
[2025-08-05T20:33:18+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_menu` [ RunTime:0.000614s ]
[2025-08-05T20:33:18+08:00][sql] SHOW FULL COLUMNS FROM `www_sys_user_log` [ RunTime:0.009138s ]
[2025-08-05T20:33:18+08:00][sql] INSERT INTO `www_sys_user_log` SET `uid` = 1 , `username` = 'admin' , `url` = '/adminapi/addon/install/yz_she' , `params` = '{\"addon\":\"yz_she\"}' , `ip` = '127.0.0.1' , `type` = 'POST' , `operation` = '插件管理-安装插件' , `create_time` = 1754397198 [ RunTime:0.000626s ]
[2025-08-05T20:33:18+08:00][sql] SHOW FULL COLUMNS FROM `www_addon` [ RunTime:0.000616s ]
[2025-08-05T20:33:18+08:00][sql] SELECT * FROM `www_addon` WHERE  `key` = 'yz_she' LIMIT 1 [ RunTime:0.000349s ]
[2025-08-05T20:33:18+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000629s ]
[2025-08-05T20:33:18+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000430s ]
[2025-08-05T20:33:18+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000452s ]
[2025-08-05T20:33:18+08:00][sql] SET FOREIGN_KEY_CHECKS = 0 [ RunTime:0.000639s ]
[2025-08-05T20:33:18+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_brands` [ RunTime:0.000331s ]
[2025-08-05T20:33:18+08:00][sql] CREATE TABLE `www_yz_she_brands`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '品牌ID',
  `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '品牌名称',
  `logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '品牌Logo',
  `letter` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'A' COMMENT '首字母索引',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '是否热门品牌 1是 0否',
  `hot_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '热门品牌名字',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1启用 0禁用',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_letter`(`letter`) USING BTREE,
  INDEX `idx_is_hot`(`is_hot`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '品牌表' ROW_FORMAT = Dynamic [ RunTime:0.020819s ]
[2025-08-05T20:33:18+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_categories` [ RunTime:0.000539s ]
[2025-08-05T20:33:18+08:00][sql] CREATE TABLE `www_yz_she_categories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类图标',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类图片',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1启用 0禁用',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收分类表' ROW_FORMAT = Dynamic [ RunTime:0.013660s ]
[2025-08-05T20:33:18+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_category_accessories` [ RunTime:0.000352s ]
[2025-08-05T20:33:18+08:00][sql] CREATE TABLE `www_yz_she_category_accessories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `accessory_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配件名称',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分类配件配置表' ROW_FORMAT = Dynamic [ RunTime:0.013719s ]
[2025-08-05T20:33:18+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_category_photos` [ RunTime:0.000363s ]
[2025-08-05T20:33:18+08:00][sql] CREATE TABLE `www_yz_she_category_photos`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `photo_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片名称',
  `background_image` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'upload/background/default-photo-bg.png' COMMENT '背景图片',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分类照片配置表' ROW_FORMAT = Dynamic [ RunTime:0.014356s ]
[2025-08-05T20:33:18+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_express_log` [ RunTime:0.000382s ]
[2025-08-05T20:33:18+08:00][sql] CREATE TABLE `www_yz_she_express_log`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint(20) UNSIGNED NOT NULL COMMENT '订单ID',
  `waybill` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运单号',
  `shopbill` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家单号',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '运单状态描述',
  `type_code` tinyint(1) NULL DEFAULT NULL COMMENT '状态码：1待揽收，2运输中，3已签收，4拒收退回，99已取消',
  `weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '下单重量',
  `real_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '站点称重',
  `transfer_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '分拣称重',
  `cal_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '计费重量',
  `volume` decimal(10, 2) NULL DEFAULT NULL COMMENT '体积',
  `parse_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '体积换算重量',
  `total_freight` decimal(10, 2) NULL DEFAULT NULL COMMENT '运单总扣款费用',
  `freight` decimal(10, 2) NULL DEFAULT NULL COMMENT '快递费',
  `freight_insured` decimal(10, 2) NULL DEFAULT NULL COMMENT '保价费',
  `freight_haocai` decimal(10, 2) NULL DEFAULT NULL COMMENT '增值费用',
  `change_bill` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '换单号',
  `change_bill_freight` decimal(10, 2) NULL DEFAULT NULL COMMENT '逆向费',
  `fee_over` tinyint(1) NULL DEFAULT NULL COMMENT '订单扣费状态：1已扣费，0冻结',
  `courier_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递员姓名',
  `courier_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递员电话',
  `pickup_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '取件码',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '回调原始内容',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_waybill`(`waybill`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '物流回调日志表' ROW_FORMAT = DYNAMIC [ RunTime:0.012796s ]
[2025-08-05T20:33:18+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_goods` [ RunTime:0.000409s ]
[2025-08-05T20:33:18+08:00][sql] CREATE TABLE `www_yz_she_goods`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `brand_id` int(11) NOT NULL DEFAULT 0 COMMENT '品牌ID',
  `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品货号/型号',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品图片JSON格式',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品描述',
  `price_new` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '全新品回收价格',
  `price_used` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '二手品回收价格',
  `price_damaged` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '有瑕疵回收价格',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '是否热门商品 1是 0否',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1上架 0下架',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_brand_id`(`brand_id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_code`(`code`) USING BTREE,
  INDEX `idx_is_hot`(`is_hot`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品表' ROW_FORMAT = Dynamic [ RunTime:0.020249s ]
[2025-08-05T20:33:18+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_quote_orders` [ RunTime:0.000434s ]
[2025-08-05T20:33:18+08:00][sql] CREATE TABLE `www_yz_she_quote_orders`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `brand_id` int(11) NULL DEFAULT NULL COMMENT '品牌ID',
  `product_id` int(11) NULL DEFAULT NULL COMMENT '商品ID',
  `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `product_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品编码',
  `product_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品图片/品牌图片',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单状态:1=估价中,2=待确认,3=待发货,4=已完成,5=已取消',
  `quote_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '估价金额',
  `user_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户备注',
  `admin_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '管理员备注',
  `admin_id` int(11) NULL DEFAULT NULL COMMENT '估价管理员ID',
  `quote_time` datetime NULL DEFAULT NULL COMMENT '估价时间',
  `confirm_time` datetime NULL DEFAULT NULL COMMENT '用户确认时间',
  `ship_time` datetime NULL DEFAULT NULL COMMENT '用户发货时间',
  `complete_time` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `cancel_time` datetime NULL DEFAULT NULL COMMENT '取消时间',
  `cancel_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '取消原因',
  `auto_cancel_time` datetime NULL DEFAULT NULL COMMENT '自动取消时间(确认后48小时)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_no`(`order_no`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  INDEX `brand_id`(`brand_id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `admin_id`(`admin_id`) USING BTREE,
  INDEX `create_time`(`create_time`) USING BTREE,
  INDEX `auto_cancel_time`(`auto_cancel_time`) USING BTREE,
  INDEX `idx_orders_user_status_time`(`user_id`, `status`, `create_time`) USING BTREE,
  INDEX `idx_orders_status_time`(`status`, `create_time`) USING BTREE,
  INDEX `idx_orders_auto_cancel`(`status`, `auto_cancel_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 37 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价订单主表' ROW_FORMAT = Dynamic [ RunTime:0.027614s ]
[2025-08-05T20:33:18+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_order_status_logs` [ RunTime:0.000411s ]
[2025-08-05T20:33:18+08:00][sql] CREATE TABLE `www_yz_she_order_status_logs`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `from_status` tinyint(1) NULL DEFAULT NULL COMMENT '原状态',
  `to_status` tinyint(1) NOT NULL COMMENT '新状态',
  `operator_id` int(11) NULL DEFAULT NULL COMMENT '操作人ID',
  `operator_type` tinyint(1) NOT NULL COMMENT '操作人类型:1=用户,2=管理员,3=系统',
  `change_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更原因',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `operator_id`(`operator_id`) USING BTREE,
  INDEX `create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价订单状态变更日志表' ROW_FORMAT = Dynamic [ RunTime:0.014938s ]
[2025-08-05T20:33:18+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_quote_accessories` [ RunTime:0.000410s ]
[2025-08-05T20:33:18+08:00][sql] CREATE TABLE `www_yz_she_quote_accessories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配件ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `accessory_config_id` int(11) NULL DEFAULT NULL COMMENT '配件配置ID',
  `accessory_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配件名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `accessory_config_id`(`accessory_config_id`) USING BTREE,
  INDEX `idx_accessories_order`(`quote_order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价订单配件表' ROW_FORMAT = Dynamic [ RunTime:0.015798s ]
[2025-08-05T20:33:18+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_quote_photos` [ RunTime:0.000543s ]
[2025-08-05T20:33:18+08:00][sql] CREATE TABLE `www_yz_she_quote_photos`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '照片ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `photo_config_id` int(11) NULL DEFAULT NULL COMMENT '照片配置ID',
  `photo_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片类型',
  `photo_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片名称',
  `photo_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片URL',
  `is_defect` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为瑕疵照片:0=否,1=是',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `photo_config_id`(`photo_config_id`) USING BTREE,
  INDEX `photo_type`(`photo_type`) USING BTREE,
  INDEX `is_defect`(`is_defect`) USING BTREE,
  INDEX `idx_photos_order_type`(`quote_order_id`, `photo_type`, `sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价订单照片表' ROW_FORMAT = Dynamic [ RunTime:0.018799s ]
[2025-08-05T20:33:18+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_quote_records` [ RunTime:0.000349s ]
[2025-08-05T20:33:18+08:00][sql] CREATE TABLE `www_yz_she_quote_records`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `admin_id` int(11) NOT NULL COMMENT '估价管理员ID',
  `condition_score` int(3) NULL DEFAULT NULL COMMENT '成色评分(0-100)',
  `quote_price` decimal(10, 2) NOT NULL COMMENT '估价金额',
  `quote_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '估价说明',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `admin_id`(`admin_id`) USING BTREE,
  INDEX `create_time`(`create_time`) USING BTREE,
  INDEX `idx_records_order_time`(`quote_order_id`, `create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价记录表' ROW_FORMAT = Dynamic [ RunTime:0.022462s ]
[2025-08-05T20:33:18+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_recycle_order_logs` [ RunTime:0.000316s ]
[2025-08-05T20:33:18+08:00][sql] CREATE TABLE `www_yz_she_recycle_order_logs`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `recycle_order_id` int(11) NOT NULL COMMENT '回收订单ID',
  `from_status` tinyint(1) NULL DEFAULT NULL COMMENT '原状态',
  `to_status` tinyint(1) NOT NULL COMMENT '新状态',
  `operator_id` int(11) NULL DEFAULT NULL COMMENT '操作人ID',
  `operator_type` tinyint(1) NOT NULL COMMENT '操作人类型:1=用户,2=管理员,3=系统',
  `change_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更原因',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `extra_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '额外数据JSON',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_recycle_order_id`(`recycle_order_id`) USING BTREE,
  INDEX `idx_operator_id`(`operator_id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 315 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收订单状态变更日志表' ROW_FORMAT = DYNAMIC [ RunTime:0.014438s ]
[2025-08-05T20:33:18+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_recycle_orders` [ RunTime:0.000322s ]
[2025-08-05T20:33:18+08:00][sql] CREATE TABLE `www_yz_she_recycle_orders`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '回收订单ID',
  `order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '回收订单编号',
  `quote_order_id` int(11) NULL DEFAULT NULL COMMENT '关联的估价订单ID（从估价订单创建时有值）',
  `member_id` int(11) NOT NULL COMMENT '会员ID（对应member表）',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `brand_id` int(11) NULL DEFAULT NULL COMMENT '品牌ID',
  `product_id` int(11) NULL DEFAULT NULL COMMENT '商品ID',
  `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `product_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品编码',
  `product_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品图片',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单状态:1=待取件,2=待收货,3=待质检,4=待确认,5=待退回,6=已退回,7=已完成,8=已取消',
  `source_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单来源:1=估价订单确认,2=直接回收,3=批量下单',
  `quantity` int(11) NOT NULL DEFAULT 1 COMMENT '回收数量',
  `expected_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '预期回收价格（用户选择的价格）',
  `final_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '最终回收价格（质检后确定）',
  `voucher_id` int(11) NULL DEFAULT NULL COMMENT '使用的加价券ID（对应www_yz_she_voucher表）',
  `voucher_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '加价券金额',
  `total_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '最终结算金额',
  `delivery_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '配送方式:1=快递上门,2=自行寄出',
  `pickup_address_id` int(11) NULL DEFAULT NULL COMMENT '上门取件地址ID（对应www_member_address表）',
  `pickup_time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '期望上门时间',
  `pickup_contact_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系人姓名',
  `pickup_contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系人电话',
  `pickup_address_detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '详细地址信息',
  `express_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递回调ID',
  `express_company` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递公司',
  `express_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递单号',
  `express_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '快递费用',
  `express_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '快递状态:0=暂无轨迹,1=已揽收,2=运输中,3=已签收,4=异常',
  `quality_check_admin_id` int(11) NULL DEFAULT NULL COMMENT '质检员ID',
  `quality_score` int(3) NULL DEFAULT NULL COMMENT '质检评分(0-100)',
  `quality_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '质检说明',
  `quality_images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '质检照片JSON',
  `settlement_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '结算方式:1=余额结算',
  `settlement_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '实际结算金额',
  `settlement_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '结算状态:0=未结算,1=已结算',
  `settlement_admin_id` int(11) NULL DEFAULT NULL COMMENT '结算操作员ID',
  `user_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户备注',
  `admin_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '管理员备注',
  `reject_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拒绝原因（用户不接受质检价格）',
  `return_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退回原因',
  `pickup_time_actual` int(11) NULL DEFAULT NULL COMMENT '实际取件时间（状态1→2）',
  `receive_time` int(11) NULL DEFAULT NULL COMMENT '平台收货时间（状态2→3）',
  `quality_start_time` int(11) NULL DEFAULT NULL COMMENT '质检开始时间（状态3→4）',
  `quality_complete_time` int(11) NULL DEFAULT NULL COMMENT '质检完成时间（状态4完成）',
  `confirm_time` int(11) NULL DEFAULT NULL COMMENT '用户确认时间（状态4→7或4→5）',
  `return_time` int(11) NULL DEFAULT NULL COMMENT '商品退回时间（状态5→6）',
  `return_express_company` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '退回快递公司',
  `return_express_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '退回快递单号',
  `return_note` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '退回备注',
  `settlement_time` int(11) NULL DEFAULT NULL COMMENT '结算完成时间（状态7）',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_no`(`order_no`) USING BTREE,
  INDEX `idx_quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `idx_member_id`(`member_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_source_type`(`source_type`) USING BTREE,
  INDEX `idx_delivery_type`(`delivery_type`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_member_status_time`(`member_id`, `status`, `create_time`) USING BTREE,
  INDEX `idx_pickup_address_id`(`pickup_address_id`) USING BTREE,
  INDEX `idx_voucher_id`(`voucher_id`) USING BTREE,
  INDEX `idx_express_status`(`express_status`) USING BTREE,
  INDEX `idx_return_express_no`(`return_express_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 76 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收订单主表' ROW_FORMAT = DYNAMIC [ RunTime:0.030348s ]
[2025-08-05T20:33:18+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_recycle_standards` [ RunTime:0.000294s ]
[2025-08-05T20:33:18+08:00][sql] CREATE TABLE `www_yz_she_recycle_standards`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '标准ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID（关联商品分类）',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标准项标题（如：外观、轻度磨损等）',
  `image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '示例图片',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '详细描述',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态：1启用 0禁用',
  `create_time` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_status_sort`(`category_id`, `status`, `sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收标准表' ROW_FORMAT = Dynamic [ RunTime:0.012011s ]
[2025-08-05T20:33:18+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_system_config` [ RunTime:0.000381s ]
[2025-08-05T20:33:18+08:00][sql] CREATE TABLE `www_yz_she_system_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `store_receiver_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货人姓名',
  `store_receiver_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货人电话',
  `store_province_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '省份名称',
  `store_city_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '城市名称',
  `store_district_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '区县名称',
  `store_address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '详细地址',
  `service_qr_code` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客服二维码',
  `service_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客服电话',
  `yunyang_appid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '云洋快递AppID',
  `yunyang_key` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '云洋快递Key',
  `yunyang_api_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '回调API地址',
  `yunyang_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否自动发单 0:禁用 1:启用',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '奢侈品系统配置表' ROW_FORMAT = Dynamic [ RunTime:0.010389s ]
[2025-08-05T20:33:18+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_voucher` [ RunTime:0.000614s ]
[2025-08-05T20:33:18+08:00][sql] CREATE TABLE `www_yz_she_voucher`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '加价券ID',
  `template_id` int(11) NOT NULL DEFAULT 0 COMMENT '模板ID',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `voucher_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '加价券编号',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '加价券标题',
  `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '类型：1-固定金额 2-百分比',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额或百分比',
  `min_condition_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最低使用金额',
  `max_discount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最大优惠金额',
  `applicable_goods_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '适用商品类型',
  `applicable_goods_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用商品ID',
  `applicable_category_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用分类ID',
  `applicable_brand_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用品牌ID',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-未使用 2-已使用 3-已过期',
  `receive_time` int(11) NOT NULL DEFAULT 0 COMMENT '发放时间',
  `use_time` int(11) NOT NULL DEFAULT 0 COMMENT '使用时间',
  `expire_time` int(11) NOT NULL DEFAULT 0 COMMENT '过期时间',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '使用的订单ID',
  `send_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '发放类型：1-注册自动发放 2-手动发放',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_voucher_no`(`voucher_no`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE,
  INDEX `idx_member_id`(`member_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_expire_time`(`expire_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 32 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户加价券表' ROW_FORMAT = Dynamic [ RunTime:0.016924s ]
[2025-08-05T20:33:18+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_voucher_send_log` [ RunTime:0.000406s ]
[2025-08-05T20:33:18+08:00][sql] CREATE TABLE `www_yz_she_voucher_send_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `template_id` int(11) NOT NULL DEFAULT 0 COMMENT '模板ID',
  `send_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '发放类型：1-注册自动发放 2-全部用户发放 3-指定用户发放',
  `send_count` int(11) NOT NULL DEFAULT 0 COMMENT '发放数量',
  `member_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '指定用户ID，JSON格式',
  `admin_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作管理员ID',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE,
  INDEX `idx_send_type`(`send_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '加价券发放记录表' ROW_FORMAT = Dynamic [ RunTime:0.013903s ]
[2025-08-05T20:33:18+08:00][sql] DROP TABLE IF EXISTS `www_yz_she_voucher_template` [ RunTime:0.000246s ]
[2025-08-05T20:33:18+08:00][sql] CREATE TABLE `www_yz_she_voucher_template`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '加价券标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '加价券描述',
  `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '类型：1-固定金额 2-百分比',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额或百分比',
  `min_condition_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最低使用金额',
  `max_discount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最大优惠金额(百分比类型时使用)',
  `sum_count` int(11) NOT NULL DEFAULT -1 COMMENT '发放总数量，-1为不限制',
  `receive_count` int(11) NOT NULL DEFAULT 0 COMMENT '已发放数量',
  `use_count` int(11) NOT NULL DEFAULT 0 COMMENT '已使用数量',
  `valid_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '有效期类型：1-领取后N天有效 2-固定时间段',
  `length` int(11) NOT NULL DEFAULT 30 COMMENT '有效天数',
  `valid_start_time` int(11) NOT NULL DEFAULT 0 COMMENT '有效期开始时间',
  `valid_end_time` int(11) NOT NULL DEFAULT 0 COMMENT '有效期结束时间',
  `applicable_goods_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '适用商品类型：1-全部商品 2-指定商品 3-指定分类 4-指定品牌',
  `applicable_goods_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用商品ID，JSON格式',
  `applicable_category_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用分类ID，JSON格式',
  `applicable_brand_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用品牌ID，JSON格式',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：0-未开始 1-进行中 2-已结束 3-已关闭',
  `auto_send_register` tinyint(4) NOT NULL DEFAULT 0 COMMENT '新用户注册自动发放：0-否 1-是',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '加价券模板表' ROW_FORMAT = Dynamic [ RunTime:0.015114s ]
[2025-08-05T20:33:18+08:00][sql] DROP TRIGGER IF EXISTS `tr_quote_orders_auto_cancel_time` [ RunTime:0.000556s ]
[2025-08-05T20:33:18+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000446s ]
[2025-08-05T20:33:18+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000296s ]
[2025-08-05T20:33:18+08:00][sql] SELECT `title`,`icon`,`key`,`desc`,`status`,`type`,`support_app` FROM `www_addon` WHERE  `status` = 1 [ RunTime:0.000297s ]
[2025-08-05T20:33:18+08:00][error] DEBUG：>>>>>>>>>
服务主体：0
IP：127.0.0.1
耗时（毫秒）：631
请求类型：POST
应用：adminapi
路由：/adminapi/addon/install/yz_she
请求参数：{"addon":"yz_she"}
错误信息：{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\service\\core\\addon\\CoreAddonInstallService.php","line":245,"message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 5","trace":[{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\service\\admin\\addon\\AddonService.php","line":60,"function":"install","class":"app\\service\\core\\addon\\CoreAddonInstallService","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\controller\\addon\\Addon.php","line":56,"function":"install","class":"app\\service\\admin\\addon\\AddonService","type":"->"},{"function":"install","class":"app\\adminapi\\controller\\addon\\Addon","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Container.php","line":345,"function":"invokeArgs","class":"ReflectionMethod","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":110,"function":"invokeReflectMethod","class":"think\\Container","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\route\\dispatch\\{closure}","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\dispatch\\Controller.php","line":113,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\route\\Dispatch.php","line":52,"function":"exec","class":"think\\route\\dispatch\\Controller","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":755,"function":"run","class":"think\\route\\Dispatch","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminLog.php","line":58,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminLog","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckRole.php","line":31,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckRole","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AdminCheckToken.php","line":32,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AdminCheckToken","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Route.php","line":756,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":208,"function":"dispatch","class":"think\\Route","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":198,"function":"dispatchToRoute","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\{closure}","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":59,"function":"think\\app\\{closure}","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\app\\adminapi\\middleware\\AllowCrossDomain.php","line":36,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"app\\adminapi\\middleware\\AllowCrossDomain","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-multi-app\\src\\MultiApp.php","line":72,"function":"then","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\app\\MultiApp","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\middleware\\LoadLangPack.php","line":53,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\middleware\\LoadLangPack","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\think-trace\\src\\TraceDebug.php","line":71,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"function":"handle","class":"think\\trace\\TraceDebug","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Middleware.php","line":134,"function":"call_user_func"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":85,"function":"think\\{closure}","class":"think\\Middleware","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Pipeline.php","line":66,"function":"think\\{closure}","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":199,"function":"then","class":"think\\Pipeline","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\vendor\\topthink\\framework\\src\\think\\Http.php","line":162,"function":"runWithRequest","class":"think\\Http","type":"->"},{"file":"D:\\phpstudy_pro\\WWW\\yy.com\\niucloud\\public\\index.php","line":24,"function":"run","class":"think\\Http","type":"->"}],"previous":null}
---------
