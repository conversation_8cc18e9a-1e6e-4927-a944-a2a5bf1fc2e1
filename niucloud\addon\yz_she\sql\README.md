# 奢侈品回收插件数据库安装说明

## 📋 安装步骤

### 1. 主体数据库安装
执行 `install.sql` 文件来创建所有数据表：
```sql
-- 直接执行 install.sql 文件即可
```

### 2. 触发器安装（可选）
如果需要触发器功能，请在主体安装完成后执行 `triggers.sql`：
```sql
-- 1. 将 triggers.sql 中的 {{prefix}} 替换为实际表前缀
-- 2. 执行修改后的 SQL 语句
-- 3. 验证：SHOW TRIGGERS LIKE '%yz_she%';
```

## 🔧 问题解决

### 外键约束错误 (SQLSTATE[HY000]: General error: 1215)
✅ **已解决** - 已优化表创建顺序，主表在子表之前创建

### 触发器创建失败
✅ **已解决** - 触发器已分离到独立文件，避免安装时冲突

## 📊 数据表结构

### 核心表
- `yz_she_categories` - 回收分类表
- `yz_she_brands` - 品牌表  
- `yz_she_goods` - 商品表

### 估价相关表
- `yz_she_quote_orders` - 估价订单主表 ⭐
- `yz_she_order_status_logs` - 估价订单状态日志
- `yz_she_quote_accessories` - 估价订单配件表
- `yz_she_quote_photos` - 估价订单照片表
- `yz_she_quote_records` - 估价记录表

### 回收相关表
- `yz_she_recycle_orders` - 回收订单主表
- `yz_she_recycle_order_logs` - 回收订单日志表

### 配置表
- `yz_she_category_accessories` - 分类配件配置
- `yz_she_category_photos` - 分类照片配置
- `yz_she_recycle_standards` - 回收标准表
- `yz_she_system_config` - 系统配置表

### 优惠券表
- `yz_she_voucher_template` - 加价券模板表
- `yz_she_voucher` - 用户加价券表
- `yz_she_voucher_send_log` - 加价券发放记录

### 物流表
- `yz_she_express_log` - 物流回调日志表

## 🔗 外键关系

```
yz_she_quote_orders (主表)
├── yz_she_order_status_logs (状态日志)
├── yz_she_quote_accessories (配件)
├── yz_she_quote_photos (照片)
└── yz_she_quote_records (估价记录)
```

## ⚠️ 注意事项

1. **表前缀**：确保 `{{prefix}}` 被正确替换
2. **权限**：数据库用户需要 CREATE、ALTER、INDEX 权限
3. **触发器权限**：创建触发器需要 TRIGGER 权限
4. **字符集**：使用 utf8mb4 字符集，支持 emoji 等特殊字符
5. **引擎**：使用 InnoDB 引擎，支持事务和外键

## 🚀 验证安装

```sql
-- 检查表是否创建成功
SHOW TABLES LIKE '%yz_she%';

-- 检查外键约束
SELECT 
    TABLE_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME
FROM 
    information_schema.KEY_COLUMN_USAGE 
WHERE 
    TABLE_SCHEMA = DATABASE() 
    AND REFERENCED_TABLE_NAME LIKE '%yz_she%';

-- 检查触发器（如果已安装）
SHOW TRIGGERS LIKE '%yz_she%';
```
