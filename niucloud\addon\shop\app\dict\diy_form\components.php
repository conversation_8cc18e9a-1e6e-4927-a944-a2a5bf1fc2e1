<?php

return [
//    'SHOP_FORM_COMPONENT' => [
//        'title' => get_lang('dict_diy_form.shop_component_type_basic'),
//        'list' => [
//            'GoodsInput' => [
//                'title' => '商品文本',
//                'icon' => 'iconfont iconbiaotipc',
//                'path' => 'edit-goods-input', // 编辑组件属性名称
//                'uses' => 0, // 最大添加数量
//                'sort' => 10001,
//                'value' => [
//                    'label' => '商品文本',
//                    'type' => 'text', // 文本、手机号、身份证号码、邮箱、数字、带小数点数字
//                    'placeholder' => '请输入文本内容', // 提示语
//                    'default' => '',
//                    'required' => true // 这个是公共字段
//                ]
//            ],
//        ]
//    ]
];
