{"addGoods": "添加商品", "updateGoods": "编辑商品", "basicInfoTab": "基础信息", "goodsType": "商品类型", "goodsName": "商品名称", "goodsNamePlaceholder": "请输入商品名称", "goodsNameMaxLengthTips": "商品名称不能超过60个字符", "subTitle": "副标题", "subTitlePlaceholder": "请输入副标题", "subTitleMaxLengthTips": "副标题不能超过80个字符", "goodsImage": "商品图片", "goodsImagePlaceholder": "请上传商品图片", "goodsVideo": "商品视频", "goodsVideoPlaceholder": "请上传商品视频", "goodsCategory": "商品分类", "refresh": "刷新", "addGoodsCategory": "添加分类", "goodsCategoryPlaceholder": "请选择商品分类", "brand": "商品品牌", "brandPlaceholder": "请选择商品品牌", "addGoodsBrand": "添加品牌", "poster": "商品海报", "posterPlaceholder": "请选择商品海报", "addGoodsPoster": "添加海报", "posterTips": "不设置将使用默认海报", "diyForm": "万能表单", "diyFormPlaceholder": "请选择万能表单", "addDiyForm": "添加表单", "label": "商品标签", "addGoodsLabel": "添加商品标签", "goodsService": "商品服务", "addGoodsService": "添加商品服务", "supplier": "供应商", "supplierPlaceholder": "请选择供应商", "addSupplier": "添加供应商", "status": "商品状态", "statusOn": "上架", "statusOff": "下架", "isGive": "是否赠品", "yes": "是", "no": "否", "sort": "排序", "sortPlaceholder": "请输入排序", "sortTips": "排序号格式输入错误", "priceStockTab": "价格库存", "specType": "规格类型", "singleSpec": "单规格", "multiSpec": "多规格", "price": "销售价", "marketPrice": "划线价", "costPrice": "成本价", "weight": "重量", "volume": "体积", "goodsStock": "商品库存", "goodsStockPlaceholder": "请输入商品库存", "skuNo": "商品编码", "yuan": "元", "defaultUnit": "件", "skuNoPlaceholder": "请输入商品编码", "goodsSku": "商品规格", "specNamePlaceholder": "请输入规格项，如颜色、尺码、大小", "specValueNamePlaceholder": "请输入规格值，如：白色", "addSpecValue": "+添加规格值", "addSpec": "添加规格", "batchOperationSku": "批量设置", "all": "全部", "stock": "库存", "skuWeight": "重量(kg)", "skuVolume": "体积(m³)", "confirm": "确定", "image": "图片", "defaultSku": "默认规格", "unit": "单位", "unitPlaceholder": "请输入单位，默认为：件", "virtualSaleNum": "虚拟销量", "virtualSaleNumPlaceholder": "请输入虚拟销量", "virtualSaleNumDesc": "虚拟销量只在前台展示中参与计算", "virtualSaleNumTips": "虚拟销量格式输入错误", "virtualSaleNumNotZeroTips": "虚拟销量不能小于0", "giftTips": "当商品设置为赠品时，该商品仅用于活动赠送，不会在前台展示或出售", "maxAddSpecTips": "最多添加5个规格项", "pleaseEditSpecPlaceholder": "请编辑规格信息", "refreshSuccess": "刷新成功", "deliveryTab": "配送设置", "deliveryType": "配送方式", "deliveryTypePlaceholder": "请选择配送方式", "pleaseSelectSku": "请先选择商品规格", "isLimit": "是否限购", "isLimitTips": "启用限购后，购买商品时，会对该商品购买量做限制判断。", "limitType": "限购类型", "limitTypeTips": "单次限购是针对于每次下单不能超过限购数量，单人限购是针对于会员账号购买这个商品的总数不能超过限购数量。", "singleTime": "单次限购", "singlePerson": "单人限购", "maxBuy": "限购数量", "maxBuyPlaceholder": "请输入限购数量", "maxBuyTips": "[限购数量]格式输入错误", "maxBuyWarnTips": "限购数量超出商品库存时，买家将无法购买该商品", "maxBuyNotZeroTips": "限购数量不能小于1", "minBuy": "起购数量", "minBuyTips": "起购数量超出商品库存时，买家将无法购买该商品", "minBuyFormatErrorTips": "[起购数量]格式输入错误", "minBuyNotZeroTips": "起购数量不能小于0", "minBuyGreaterThanMaxBuyTips": "起购数量不能大于限购数量", "isFreeShipping": "是否免邮", "feeType": "运费设置", "selectTemplate": "选择模板", "fixedShipping": "统一运费", "deliveryMoney": "固定运费", "deliveryMoneyPlaceholder": "请输入固定运费", "deliveryMoneyTips": "固定运费格式输入错误", "deliveryMoneyNotZeroTips": "固定运费不能小于0", "deliveryTemplateId": "运费模板", "deliveryTemplateIdPlaceholder": "请选择运费模板", "addDeliveryTemplateId": "添加运费模板", "goodsDesc": "商品详情", "goodsDescPlaceholder": "请填写商品详情", "goodsDescMaxTips": "商品描述字符数应在5～50000之间", "pricePlaceholder": "请输入销售价", "priceTips": "[销售价]格式输入错误", "priceNotZeroTips": "销售价不能小于0", "marketPricePlaceholder": "请输入划线价", "marketPriceTips": "[划线价]格式输入错误", "marketPriceNotZeroTips": "划线价不能小于0", "costPricePlaceholder": "请输入成本价", "costPriceTips": "[成本价]格式输入错误", "costPriceNotZeroTips": "成本价不能小于0", "weightPlaceholder": "请输入重量", "weightTips": "[重量(kg)]格式输入错误", "weightNotZeroTips": "重量(kg)不能小于0", "volumePlaceholder": "请输入体积", "volumeTips": "[体积(m³)]格式输入错误", "volumeNotZeroTips": "体积(m³)不能小于0", "stockPlaceholder": "请输入库存", "stockTips": "[库存]格式输入错误", "stockNotZeroTips": "库存不能小于0", "specNameRequire": "规格项不能为空", "specNameRepeat": "规格项不能重复", "specValueRequire": "规格值不能为空", "specValueNameRepeat": "规格值不能重复", "lackDefaultSpec": "商品缺少默认规格", "goodsArguments": "商品参数", "goodsArgumentsTemp": "商品参数模板", "goodsArgumentsTempPlaceholder": "请选择商品参数模板", "goodsArgumentsTempHint": "商品可以添加自定义商品参数，也可以通过参数模板批量设置商品参数", "argumentsName": "参数名", "argumentsValue": "参数值", "argumentsSortHint": "设置排序，改变商品规格展示顺序", "operation": "操作", "delAttr": "删除", "noData": "无数据", "addGoodsArguments": "添加商品参数", "memberDiscount": "会员等级折扣", "discount": "会员折扣", "fixedPrice": "指定会员价", "nonparticipation": "不参与", "discountHint": "会员折扣说明：按照默认会员等级折扣优惠", "fixedPriceHint": "会员价说明：指定优惠价格，商品未参与活动时，按照会员价优惠，若商品参与活动，则以活动价为准", "participateInActiveDisableTips": "商品正在参与营销活动，禁止操作"}